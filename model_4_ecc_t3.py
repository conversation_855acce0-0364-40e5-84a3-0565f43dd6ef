#!/usr/bin/env python3
"""
Model 4: ECC_T3 - Exact Architecture Implementation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import NNConv
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
from typing import List, Tuple
import warnings
warnings.filterwarnings('ignore')

class ECC_T3_Model(nn.Module):
    """Exact architecture for ECC_T3 model."""
    
    def __init__(self, input_dim=10, hidden_dim=64, num_layers=3):
        super().__init__()
        
        # Embedding layer: input_dim -> hidden_dim
        self.embedding = nn.Linear(input_dim, hidden_dim)
        
        # 3 ECC convolution layers
        self.convs = nn.ModuleList()
        for _ in range(num_layers):
            # Edge network: 1 -> 4096 -> 4096
            edge_nn = nn.Sequential(
                nn.Linear(1, 4096),
                nn.<PERSON>L<PERSON>(),
                nn.Linear(4096, 4096)
            )
            self.convs.append(NNConv(hidden_dim, hidden_dim, edge_nn, aggr='mean', root_weight=True))
        
        # Batch normalization layers
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers):
            self.batch_norms.append(nn.ModuleDict({'module': nn.BatchNorm1d(hidden_dim)}))
        
        # MLP classifier: 128 -> 64 -> 1 (concatenated features)
        self.mlp = nn.Sequential(
            nn.Linear(128, 64),  # Concatenated initial + final features
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 1)
        )
    
    def forward(self, data):
        x, edge_index, batch = data.x, data.edge_index, getattr(data, 'batch', None)
        
        # Handle feature dimension (test data has 14, model expects 10)
        if x.shape[1] > 10:
            x = x[:, :10]
        
        # Create edge attributes (distance between connected nodes)
        row, col = edge_index
        edge_attr = torch.norm(data.pos[row] - data.pos[col], dim=1, keepdim=True)
        
        # Embedding
        x = self.embedding(x)
        x_initial = x.clone()  # Store initial features
        
        # ECC layers with batch norm
        for conv, bn in zip(self.convs, self.batch_norms):
            x = conv(x, edge_index, edge_attr)
            x = bn['module'](x)
            x = F.relu(x)
        
        # Concatenate initial and final features (64 + 64 = 128)
        x_concat = torch.cat([x_initial, x], dim=1)
        
        # MLP classifier
        x = self.mlp(x_concat)
        
        return torch.sigmoid(x.squeeze())


def load_all_test_data_t3():
    """Load ALL test data for temporal window 3."""
    test_dir = 'data/07_gnn_ready/test/temporal_3'
    test_files = glob.glob(os.path.join(test_dir, '*.pt'))
    
    print(f"Loading ALL test data from {test_dir}")
    print(f"Found {len(test_files)} .pt files")
    
    test_data = []
    loaded_count = 0
    failed_count = 0
    
    for i, file_path in enumerate(sorted(test_files)):
        try:
            data = torch.load(file_path, map_location='cpu', weights_only=False)
            test_data.append(data)
            loaded_count += 1
            
            if (loaded_count % 500) == 0:
                print(f"  Loaded {loaded_count}/{len(test_files)} files...")
                
        except Exception as e:
            print(f"Warning: Could not load {file_path}: {e}")
            failed_count += 1
            continue
    
    print(f"✅ Loaded {loaded_count} files, {failed_count} failed")
    return test_data


def run_model_4_evaluation():
    """Run complete evaluation for Model 4: ECC_T3."""
    print("🎯 MODEL 4: ECC_T3 - COMPLETE EVALUATION")
    print("=" * 60)
    
    # Load model
    checkpoint_path = 'models_final/checkpoints_ecc_temp3/model_temporal_3_best.pt'
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint not found: {checkpoint_path}")
        return None
    
    # Create model
    model = ECC_T3_Model()
    
    # Load weights
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        print(f"✅ Model loaded successfully")
        print(f"   Validation F1: {checkpoint.get('val_f1', 0):.3f}")
        print(f"   Epoch: {checkpoint.get('epoch', 0)}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None
    
    # Load ALL test data
    test_data = load_all_test_data_t3()
    if not test_data:
        print("❌ No test data loaded")
        return None
    
    print(f"📊 Running inference on {len(test_data)} test samples...")
    
    # Run inference
    model.eval()
    all_predictions = []
    all_labels = []
    all_positions = []
    
    with torch.no_grad():
        for i, data in enumerate(test_data):
            try:
                # Ensure batch attribute
                if not hasattr(data, 'batch'):
                    data.batch = torch.zeros(data.x.size(0), dtype=torch.long)
                
                # Run inference
                predictions = model(data)
                
                # Convert labels to binary
                binary_labels = (data.y > 0).float()
                
                # Store results
                all_predictions.append(predictions.cpu())
                all_labels.append(binary_labels.cpu())
                all_positions.append(data.pos.cpu())
                
                if (i + 1) % 500 == 0:
                    print(f"  Processed {i + 1}/{len(test_data)} samples...")
                    
            except Exception as e:
                print(f"Error processing sample {i}: {e}")
                continue
    
    if not all_predictions:
        print("❌ No successful predictions")
        return None
    
    # Concatenate results
    predictions = torch.cat(all_predictions, dim=0)
    labels = torch.cat(all_labels, dim=0)
    positions = torch.cat(all_positions, dim=0)
    
    print(f"✅ Inference complete: {len(predictions)} total predictions")
    
    # Calculate metrics
    pred_binary = (predictions > 0.5).float()
    accuracy = torch.mean((pred_binary == labels).float()).item()
    
    # Confusion matrix
    tp = torch.sum((pred_binary == 1) & (labels == 1)).item()
    tn = torch.sum((pred_binary == 0) & (labels == 0)).item()
    fp = torch.sum((pred_binary == 1) & (labels == 0)).item()
    fn = torch.sum((pred_binary == 0) & (labels == 1)).item()
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    metrics = {
        'model_name': 'ECC_T3',
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': {'TP': int(tp), 'TN': int(tn), 'FP': int(fp), 'FN': int(fn)},
        'total_samples': len(labels),
        'occupied_samples': int(torch.sum(labels)),
        'test_files_processed': len(test_data),
        'prediction_stats': {
            'mean': float(torch.mean(predictions)),
            'std': float(torch.std(predictions)),
            'min': float(torch.min(predictions)),
            'max': float(torch.max(predictions))
        }
    }
    
    # Print results
    print(f"\n📊 MODEL 4 RESULTS:")
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Accuracy: {accuracy:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   Total samples: {len(labels):,}")
    print(f"   Test files: {len(test_data):,}")
    print(f"   Prediction range: [{metrics['prediction_stats']['min']:.3f}, {metrics['prediction_stats']['max']:.3f}]")
    
    # Create spatial visualization
    create_spatial_visualization(predictions, labels, positions, metrics)
    
    return {
        'metrics': metrics,
        'predictions': predictions,
        'labels': labels,
        'positions': positions
    }


def create_spatial_visualization(predictions, labels, positions, metrics):
    """Create spatial visualization for Model 4."""
    print("🎨 Creating spatial visualization...")
    
    os.makedirs('model_4_results', exist_ok=True)
    
    # Create side-by-side visualization
    fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Convert to numpy
    positions_np = positions.numpy()
    labels_np = labels.numpy()
    predictions_np = predictions.numpy()
    
    # Left plot: Ground Truth
    occupied_mask = labels_np == 1
    unoccupied_mask = labels_np == 0
    
    if np.any(unoccupied_mask):
        ax_left.scatter(positions_np[unoccupied_mask, 0], positions_np[unoccupied_mask, 1], 
                       c='lightblue', s=8, alpha=0.6, label='Unoccupied')
    
    if np.any(occupied_mask):
        ax_left.scatter(positions_np[occupied_mask, 0], positions_np[occupied_mask, 1], 
                       c='lightcoral', s=8, alpha=0.6, label='Occupied')
    
    ax_left.set_title(f'Ground Truth\n{len(labels_np):,} nodes, {torch.sum(labels).item():,} occupied ({torch.mean(labels):.1%})', 
                     fontsize=14, fontweight='bold')
    ax_left.set_xlabel('X Position (m)')
    ax_left.set_ylabel('Y Position (m)')
    ax_left.legend()
    ax_left.grid(True, alpha=0.3)
    ax_left.set_aspect('equal')
    
    # Right plot: Model Predictions
    scatter = ax_right.scatter(positions_np[:, 0], positions_np[:, 1], 
                             c=predictions_np, cmap='RdYlBu_r', 
                             s=8, alpha=0.6, vmin=0, vmax=1)
    
    cbar = plt.colorbar(scatter, ax=ax_right, shrink=0.8)
    cbar.set_label('Prediction Probability')
    
    ax_right.set_title(f'ECC_T3 REAL Predictions\nF1: {metrics["f1_score"]:.3f}, Acc: {metrics["accuracy"]:.3f}', 
                      fontsize=14, fontweight='bold')
    ax_right.set_xlabel('X Position (m)')
    ax_right.set_ylabel('Y Position (m)')
    ax_right.grid(True, alpha=0.3)
    ax_right.set_aspect('equal')
    
    # Set consistent limits
    x_min, x_max = positions_np[:, 0].min() - 1, positions_np[:, 0].max() + 1
    y_min, y_max = positions_np[:, 1].min() - 1, positions_np[:, 1].max() + 1
    
    for ax in [ax_left, ax_right]:
        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)
    
    fig.suptitle(f'Model 4: ECC_T3 - ALL {metrics["test_files_processed"]:,} Test Files', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('model_4_results/model_4_spatial_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Saved spatial visualization: model_4_results/model_4_spatial_visualization.png")


if __name__ == "__main__":
    result = run_model_4_evaluation()
    if result:
        print("\n🎉 Model 4 evaluation complete!")
    else:
        print("\n❌ Model 4 evaluation failed!")
