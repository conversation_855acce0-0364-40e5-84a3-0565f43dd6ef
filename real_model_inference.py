#!/usr/bin/env python3
"""
Real Model Inference and Evaluation
Actually loads trained models and runs inference on test data.
No fake predictions - real model evaluation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATv2Conv, GCNConv, global_mean_pool, global_max_pool
from torch_geometric.data import Data, DataLoader
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
import yaml
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class RealModelLoader:
    """Load and run actual trained models."""
    
    def __init__(self):
        # Model paths and configurations
        self.model_configs = {
            'GATv2_T3_Standard': {
                'checkpoint_path': 'models_final/checkpoints_gatv2_temp3/model_temporal_3_best.pt',
                'config_path': 'models_final/checkpoints_gatv2_temp3/config.yaml',
                'temporal_window': 3,
                'architecture': 'GATv2'
            },
            'GATv2_T3_Complex_4Layer': {
                'checkpoint_path': 'models_final/checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt',
                'config_path': 'models_final/checkpoints_gatv2_complex_4layers_temp3/config.yaml',
                'temporal_window': 3,
                'architecture': 'GATv2'
            },
            'GATv2_T5_Standard': {
                'checkpoint_path': 'models_final/checkpoints_temp5/model_temporal_5_best.pt',
                'config_path': 'models_final/checkpoints_temp5/config.yaml',
                'temporal_window': 5,
                'architecture': 'GATv2'
            },
            'ECC_T3': {
                'checkpoint_path': 'models_final/checkpoints_ecc_temp3/model_temporal_3_best.pt',
                'config_path': None,
                'temporal_window': 3,
                'architecture': 'ECC'
            },
            'ECC_T5': {
                'checkpoint_path': 'models_final/checkpoints_ecc_temp5/model_temporal_5_best.pt',
                'config_path': None,
                'temporal_window': 5,
                'architecture': 'ECC'
            }
        }
    
    def check_model_files(self):
        """Check which model files actually exist."""
        print("🔍 CHECKING MODEL FILES")
        print("=" * 50)
        
        available_models = {}
        
        for model_name, config in self.model_configs.items():
            checkpoint_path = config['checkpoint_path']
            config_path = config.get('config_path')
            
            checkpoint_exists = os.path.exists(checkpoint_path)
            config_exists = os.path.exists(config_path) if config_path else True
            
            print(f"{model_name}:")
            print(f"  Checkpoint: {'✅' if checkpoint_exists else '❌'} {checkpoint_path}")
            if config_path:
                print(f"  Config: {'✅' if config_exists else '❌'} {config_path}")
            
            if checkpoint_exists:
                available_models[model_name] = config
                
                # Try to load checkpoint to get info
                try:
                    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
                    print(f"  Validation F1: {checkpoint.get('val_f1', 'N/A')}")
                    print(f"  Epoch: {checkpoint.get('epoch', 'N/A')}")
                    print(f"  Model state dict keys: {len(checkpoint.get('model_state_dict', {}).keys())} layers")
                except Exception as e:
                    print(f"  Error loading checkpoint: {e}")
            
            print()
        
        print(f"Available models: {len(available_models)}/{len(self.model_configs)}")
        return available_models
    
    def load_test_data(self, temporal_window: int, max_files: int = 100):
        """Load test data for the specified temporal window."""
        test_dir = f'data/07_gnn_ready/test/temporal_{temporal_window}'
        test_files = glob.glob(os.path.join(test_dir, '*.pt'))
        
        print(f"Loading test data from {test_dir}")
        print(f"Found {len(test_files)} .pt files, loading first {max_files}")
        
        test_data = []
        loaded_count = 0
        
        for file_path in sorted(test_files)[:max_files]:
            try:
                data = torch.load(file_path, map_location='cpu', weights_only=False)
                test_data.append(data)
                loaded_count += 1
                
                if loaded_count % 20 == 0:
                    print(f"  Loaded {loaded_count}/{max_files} files...")
                    
            except Exception as e:
                print(f"Warning: Could not load {file_path}: {e}")
                continue
        
        print(f"✅ Loaded {len(test_data)} test files")
        
        if test_data:
            # Analyze first file to understand data structure
            sample = test_data[0]
            print(f"Sample data structure:")
            print(f"  x.shape: {sample.x.shape}")
            print(f"  y.shape: {sample.y.shape}")
            print(f"  pos.shape: {sample.pos.shape}")
            print(f"  edge_index.shape: {sample.edge_index.shape}")
            print(f"  Unique labels: {torch.unique(sample.y).tolist()}")
        
        return test_data
    
    def create_model_from_checkpoint(self, model_name: str, checkpoint_path: str, sample_data: Data):
        """Create model architecture and load weights from checkpoint."""
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            
            # Get input dimensions from sample data
            input_dim = sample_data.x.shape[1]
            
            print(f"Creating model for {model_name}")
            print(f"  Input dimension: {input_dim}")
            print(f"  Architecture: {self.model_configs[model_name]['architecture']}")
            
            # Create model based on architecture
            if 'GATv2' in model_name:
                model = self.create_gatv2_model(input_dim, model_name)
            elif 'ECC' in model_name:
                model = self.create_ecc_model(input_dim)
            else:
                print(f"Unknown architecture for {model_name}")
                return None
            
            # Try to load state dict
            if 'model_state_dict' in checkpoint:
                try:
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"  ✅ Loaded model weights successfully")
                except Exception as e:
                    print(f"  ❌ Error loading weights: {e}")
                    print(f"  Model keys: {list(model.state_dict().keys())[:5]}...")
                    print(f"  Checkpoint keys: {list(checkpoint['model_state_dict'].keys())[:5]}...")
                    return None
            else:
                print(f"  ❌ No model_state_dict found in checkpoint")
                return None
            
            model.eval()
            return model
            
        except Exception as e:
            print(f"Error creating model {model_name}: {e}")
            return None
    
    def create_gatv2_model(self, input_dim: int, model_name: str):
        """Create GATv2 model architecture."""
        
        class GATv2Model(nn.Module):
            def __init__(self, input_dim, hidden_dim=64, num_heads=4, num_layers=2):
                super().__init__()
                
                if 'Complex' in model_name:
                    hidden_dim = 128
                    num_layers = 4
                
                self.convs = nn.ModuleList()
                self.convs.append(GATv2Conv(input_dim, hidden_dim, heads=num_heads, concat=True))
                
                for _ in range(num_layers - 1):
                    self.convs.append(GATv2Conv(hidden_dim * num_heads, hidden_dim, heads=num_heads, concat=True))
                
                self.classifier = nn.Linear(hidden_dim * num_heads, 1)
                self.dropout = nn.Dropout(0.2)
                
            def forward(self, data):
                x, edge_index, batch = data.x, data.edge_index, getattr(data, 'batch', None)
                
                for conv in self.convs:
                    x = F.relu(conv(x, edge_index))
                    x = self.dropout(x)
                
                # Node-level prediction
                x = self.classifier(x)
                return torch.sigmoid(x.squeeze())
        
        return GATv2Model(input_dim)
    
    def create_ecc_model(self, input_dim: int):
        """Create ECC model architecture."""
        
        class ECCModel(nn.Module):
            def __init__(self, input_dim, hidden_dim=64):
                super().__init__()
                
                self.conv1 = GCNConv(input_dim, hidden_dim)
                self.conv2 = GCNConv(hidden_dim, hidden_dim)
                self.conv3 = GCNConv(hidden_dim, hidden_dim)
                self.classifier = nn.Linear(hidden_dim, 1)
                self.dropout = nn.Dropout(0.2)
                
            def forward(self, data):
                x, edge_index, batch = data.x, data.edge_index, getattr(data, 'batch', None)
                
                x = F.relu(self.conv1(x, edge_index))
                x = self.dropout(x)
                x = F.relu(self.conv2(x, edge_index))
                x = self.dropout(x)
                x = self.conv3(x, edge_index)
                
                # Node-level prediction
                x = self.classifier(x)
                return torch.sigmoid(x.squeeze())
        
        return ECCModel(input_dim)
    
    def run_model_inference(self, model: nn.Module, test_data: List[Data], model_name: str):
        """Run inference on test data with the loaded model."""
        print(f"Running inference for {model_name}...")
        
        model.eval()
        all_predictions = []
        all_labels = []
        all_positions = []
        
        with torch.no_grad():
            for i, data in enumerate(test_data):
                try:
                    # Ensure data has required attributes
                    if not hasattr(data, 'batch'):
                        data.batch = torch.zeros(data.x.size(0), dtype=torch.long)
                    
                    # Run inference
                    predictions = model(data)
                    
                    # Convert labels to binary (occupied vs unoccupied)
                    binary_labels = (data.y > 0).float()
                    
                    # Store results
                    all_predictions.append(predictions.cpu())
                    all_labels.append(binary_labels.cpu())
                    all_positions.append(data.pos.cpu())
                    
                    if (i + 1) % 20 == 0:
                        print(f"  Processed {i + 1}/{len(test_data)} samples...")
                        
                except Exception as e:
                    print(f"Error processing sample {i}: {e}")
                    continue
        
        if all_predictions:
            predictions = torch.cat(all_predictions, dim=0)
            labels = torch.cat(all_labels, dim=0)
            positions = torch.cat(all_positions, dim=0)
            
            print(f"✅ Inference complete: {len(predictions)} predictions")
            return predictions, labels, positions
        else:
            print(f"❌ No successful predictions for {model_name}")
            return None, None, None
    
    def calculate_metrics(self, predictions: torch.Tensor, labels: torch.Tensor, model_name: str):
        """Calculate evaluation metrics."""
        
        # Binary predictions (threshold at 0.5)
        pred_binary = (predictions > 0.5).float()
        
        # Basic metrics
        accuracy = torch.mean((pred_binary == labels).float()).item()
        
        # Confusion matrix
        tp = torch.sum((pred_binary == 1) & (labels == 1)).item()
        tn = torch.sum((pred_binary == 0) & (labels == 0)).item()
        fp = torch.sum((pred_binary == 1) & (labels == 0)).item()
        fn = torch.sum((pred_binary == 0) & (labels == 1)).item()
        
        # Derived metrics
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        metrics = {
            'model_name': model_name,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'confusion_matrix': {'TP': int(tp), 'TN': int(tn), 'FP': int(fp), 'FN': int(fn)},
            'total_samples': len(labels),
            'occupied_samples': int(torch.sum(labels)),
            'prediction_mean': float(torch.mean(predictions)),
            'prediction_std': float(torch.std(predictions))
        }
        
        return metrics

    def evaluate_all_real_models(self):
        """Evaluate all available models with real inference."""
        print("🎯 REAL MODEL INFERENCE AND EVALUATION")
        print("=" * 60)
        print("Loading actual trained models and running inference")
        print("No fake predictions - real model evaluation")
        print("=" * 60)

        # Check which models are available
        available_models = self.check_model_files()

        if not available_models:
            print("❌ No model files found!")
            return {}

        all_results = {}

        for model_name, config in available_models.items():
            print(f"\n📊 Evaluating {model_name} with REAL model inference...")

            # Load test data
            test_data = self.load_test_data(config['temporal_window'], max_files=50)

            if not test_data:
                print(f"❌ No test data for {model_name}")
                continue

            # Create and load model
            model = self.create_model_from_checkpoint(
                model_name, config['checkpoint_path'], test_data[0]
            )

            if model is None:
                print(f"❌ Could not load model {model_name}")
                continue

            # Run real inference
            predictions, labels, positions = self.run_model_inference(model, test_data, model_name)

            if predictions is None:
                print(f"❌ Inference failed for {model_name}")
                continue

            # Calculate metrics
            metrics = self.calculate_metrics(predictions, labels, model_name)

            # Store results
            all_results[model_name] = {
                'metrics': metrics,
                'predictions': predictions,
                'labels': labels,
                'positions': positions,
                'model_config': config,
                'num_test_files': len(test_data)
            }

            # Print results
            print(f"   ✅ REAL F1-Score: {metrics['f1_score']:.3f}")
            print(f"   ✅ REAL Accuracy: {metrics['accuracy']:.3f}")
            print(f"   ✅ REAL Precision: {metrics['precision']:.3f}")
            print(f"   ✅ REAL Recall: {metrics['recall']:.3f}")
            print(f"   📊 Samples: {metrics['total_samples']}, Occupied: {metrics['occupied_samples']}")

        return all_results

    def create_real_spatial_visualization(self, results: Dict, save_dir: str = 'real_model_evaluation'):
        """Create spatial visualizations using REAL model predictions."""
        os.makedirs(save_dir, exist_ok=True)

        if not results:
            print("No results to visualize")
            return

        print(f"\n🎨 Creating spatial visualizations with REAL model predictions...")

        for model_name, result in results.items():
            print(f"Creating visualization for {model_name}...")

            predictions = result['predictions']
            labels = result['labels']
            positions = result['positions']
            metrics = result['metrics']

            # Create side-by-side visualization
            fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))

            # Convert to numpy
            positions_np = positions.numpy()
            labels_np = labels.numpy()
            predictions_np = predictions.numpy()

            # Left plot: Ground Truth
            occupied_mask = labels_np == 1
            unoccupied_mask = labels_np == 0

            if np.any(unoccupied_mask):
                ax_left.scatter(positions_np[unoccupied_mask, 0], positions_np[unoccupied_mask, 1],
                               c='lightblue', s=20, alpha=0.8, label='Unoccupied', edgecolors='blue', linewidth=0.3)

            if np.any(occupied_mask):
                ax_left.scatter(positions_np[occupied_mask, 0], positions_np[occupied_mask, 1],
                               c='lightcoral', s=20, alpha=0.8, label='Occupied', edgecolors='red', linewidth=0.3)

            ax_left.set_title(f'Ground Truth\n{len(labels_np)} nodes, {torch.sum(labels).item()} occupied ({torch.mean(labels):.1%})',
                             fontsize=14, fontweight='bold')
            ax_left.set_xlabel('X Position (m)', fontsize=12)
            ax_left.set_ylabel('Y Position (m)', fontsize=12)
            ax_left.legend(fontsize=10)
            ax_left.grid(True, alpha=0.3)
            ax_left.set_aspect('equal')

            # Right plot: REAL Model Predictions
            scatter = ax_right.scatter(positions_np[:, 0], positions_np[:, 1],
                                     c=predictions_np, cmap='RdYlBu_r',
                                     s=20, alpha=0.8, vmin=0, vmax=1,
                                     edgecolors='black', linewidth=0.2)

            # Add colorbar
            cbar = plt.colorbar(scatter, ax=ax_right, shrink=0.8)
            cbar.set_label('REAL Model Prediction Probability', fontsize=10)

            ax_right.set_title(f'{model_name} REAL Predictions\nF1: {metrics["f1_score"]:.3f}, Acc: {metrics["accuracy"]:.3f}, Prec: {metrics["precision"]:.3f}, Rec: {metrics["recall"]:.3f}',
                              fontsize=14, fontweight='bold')
            ax_right.set_xlabel('X Position (m)', fontsize=12)
            ax_right.set_ylabel('Y Position (m)', fontsize=12)
            ax_right.grid(True, alpha=0.3)
            ax_right.set_aspect('equal')

            # Set consistent axis limits
            x_min, x_max = positions_np[:, 0].min() - 1, positions_np[:, 0].max() + 1
            y_min, y_max = positions_np[:, 1].min() - 1, positions_np[:, 1].max() + 1

            for ax in [ax_left, ax_right]:
                ax.set_xlim(x_min, x_max)
                ax.set_ylim(y_min, y_max)

            # Add model information
            config = result['model_config']
            fig.suptitle(f'REAL Model Evaluation: {model_name}\n'
                        f'Architecture: {config["architecture"]}, Temporal Window: T{config["temporal_window"]}, '
                        f'Test Files: {result["num_test_files"]}',
                        fontsize=16, fontweight='bold')

            # Add statistics
            stats_text = f'REAL MODEL INFERENCE:\n'
            stats_text += f'Total Nodes: {metrics["total_samples"]:,}\n'
            stats_text += f'Occupied: {metrics["occupied_samples"]:,} ({metrics["occupied_samples"]/metrics["total_samples"]:.1%})\n'
            stats_text += f'Prediction Mean: {metrics["prediction_mean"]:.3f}\n'
            stats_text += f'Prediction Std: {metrics["prediction_std"]:.3f}\n'
            stats_text += f'Threshold: 0.5'

            fig.text(0.02, 0.02, stats_text, fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8))

            plt.tight_layout()
            plt.subplots_adjust(top=0.85, bottom=0.15)

            # Save visualization
            filename = f'real_spatial_viz_{model_name}.png'
            filepath = os.path.join(save_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✅ Saved REAL spatial visualization: {filepath}")

    def generate_real_evaluation_report(self, results: Dict, save_dir: str = 'real_model_evaluation'):
        """Generate report for real model evaluation."""
        os.makedirs(save_dir, exist_ok=True)

        report_path = os.path.join(save_dir, 'REAL_MODEL_EVALUATION_REPORT.md')

        model_names = list(results.keys())
        f1_scores = [results[name]['metrics']['f1_score'] for name in model_names]
        sorted_indices = sorted(range(len(model_names)), key=lambda i: f1_scores[i], reverse=True)

        with open(report_path, 'w') as f:
            f.write("# REAL Model Inference and Evaluation Report\n")
            f.write("## Actual Trained Models - No Fake Predictions\n\n")

            f.write("### 🎯 Evaluation Method\n\n")
            f.write("**REAL MODEL INFERENCE**: Loaded actual trained model checkpoints and ran inference\n")
            f.write("- Loaded model weights from .pt checkpoint files\n")
            f.write("- Created proper model architectures (GATv2, ECC)\n")
            f.write("- Ran forward pass on test data\n")
            f.write("- No synthetic or fake predictions\n\n")

            f.write("### 🏆 REAL Model Performance Ranking\n\n")
            f.write("| Rank | Model | F1-Score | Accuracy | Precision | Recall | Samples | Architecture |\n")
            f.write("|------|-------|----------|----------|-----------|--------|---------|-------------|\n")

            for rank, idx in enumerate(sorted_indices, 1):
                name = model_names[idx]
                metrics = results[name]['metrics']
                config = results[name]['model_config']
                f.write(f"| {rank} | {name} | {metrics['f1_score']:.3f} | {metrics['accuracy']:.3f} | "
                       f"{metrics['precision']:.3f} | {metrics['recall']:.3f} | "
                       f"{metrics['total_samples']:,} | {config['architecture']} |\n")

            f.write("\n### 📊 Detailed REAL Results\n\n")

            for idx in sorted_indices:
                name = model_names[idx]
                metrics = results[name]['metrics']
                config = results[name]['model_config']

                f.write(f"#### {name}\n")
                f.write(f"**REAL Performance**:\n")
                f.write(f"- F1-Score: {metrics['f1_score']:.3f}\n")
                f.write(f"- Accuracy: {metrics['accuracy']:.3f}\n")
                f.write(f"- Precision: {metrics['precision']:.3f}\n")
                f.write(f"- Recall: {metrics['recall']:.3f}\n\n")

                cm = metrics['confusion_matrix']
                f.write(f"**Confusion Matrix**:\n")
                f.write(f"- True Positives: {cm['TP']}\n")
                f.write(f"- True Negatives: {cm['TN']}\n")
                f.write(f"- False Positives: {cm['FP']}\n")
                f.write(f"- False Negatives: {cm['FN']}\n\n")

                f.write(f"**Model Info**:\n")
                f.write(f"- Architecture: {config['architecture']}\n")
                f.write(f"- Temporal Window: T{config['temporal_window']}\n")
                f.write(f"- Test Files: {results[name]['num_test_files']}\n")
                f.write(f"- Total Samples: {metrics['total_samples']:,}\n")
                f.write(f"- Prediction Statistics: Mean={metrics['prediction_mean']:.3f}, Std={metrics['prediction_std']:.3f}\n\n")

            f.write("### ✅ Verification of REAL Inference\n\n")
            f.write("This evaluation uses ACTUAL trained models:\n")
            f.write("1. ✅ Loaded model checkpoints from .pt files\n")
            f.write("2. ✅ Created proper model architectures\n")
            f.write("3. ✅ Loaded trained weights into models\n")
            f.write("4. ✅ Ran forward pass inference on test data\n")
            f.write("5. ✅ Generated real predictions (not synthetic)\n\n")

            f.write("---\n")
            f.write("*REAL model evaluation using actual trained checkpoints*\n")

        print(f"✅ Generated REAL evaluation report: {report_path}")


def main():
    """Main execution function."""
    print("🎯 REAL MODEL INFERENCE AND EVALUATION")
    print("=" * 60)
    print("Loading ACTUAL trained models and running inference")
    print("No fake predictions - real model evaluation")
    print("=" * 60)

    # Initialize real model loader
    loader = RealModelLoader()

    # Evaluate all real models
    results = loader.evaluate_all_real_models()

    if not results:
        print("❌ No models evaluated successfully")
        return

    # Create spatial visualizations with REAL predictions
    print("\n🎨 Creating spatial visualizations with REAL model predictions...")
    loader.create_real_spatial_visualization(results)

    # Generate report
    print("\n📝 Generating REAL evaluation report...")
    loader.generate_real_evaluation_report(results)

    print("\n🎉 REAL Model Evaluation Complete!")
    print("=" * 60)
    print("📁 Results saved in 'real_model_evaluation/' directory:")
    print("   - real_spatial_viz_[model].png (REAL model visualizations)")
    print("   - REAL_MODEL_EVALUATION_REPORT.md (REAL performance report)")

    # Print summary
    print(f"\n💡 REAL EVALUATION SUMMARY:")
    model_names = list(results.keys())
    f1_scores = [results[name]['metrics']['f1_score'] for name in model_names]
    sorted_indices = sorted(range(len(model_names)), key=lambda i: f1_scores[i], reverse=True)

    print(f"   📊 Successfully evaluated {len(results)} REAL models")
    if sorted_indices:
        best_model = model_names[sorted_indices[0]]
        best_f1 = f1_scores[sorted_indices[0]]
        print(f"   🏆 Best REAL model: {best_model} (F1={best_f1:.3f})")

    print(f"\n   🖼️ REAL Model Performance:")
    for idx in sorted_indices:
        name = model_names[idx]
        f1 = results[name]['metrics']['f1_score']
        acc = results[name]['metrics']['accuracy']
        samples = results[name]['metrics']['total_samples']
        print(f"      - {name}: F1={f1:.3f}, Acc={acc:.3f}, Samples={samples:,}")


if __name__ == "__main__":
    main()
