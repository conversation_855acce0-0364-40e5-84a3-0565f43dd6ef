# REAL GNN Model Performance Report
## Extracted from Actual Trained Model Checkpoints

### 🎯 Executive Summary

**Total Models Trained**: 7
**Successfully Trained**: 5 (71.4%)
**Training Failures**: 2 (28.6%)

### 🏆 Top Performing Models

1. **Best Overall**: GATv2_T5_Standard
   - F1 Score: 0.673
   - Parameters: 35,140
   - Architecture: GATv2
   - Description: GATv2 Standard T5 - BEST F1 SCORE

2. **Most Efficient**: GATv2_T5_Standard
   - F1 Score: 0.673
   - Parameters: 35,140
   - Efficiency Ratio: 19.15 F1/M params

### 📊 Complete Performance Ranking

| Rank | Model | Architecture | F1 Score | Parameters | Efficiency |
|------|-------|--------------|----------|------------|------------|
| 1 | GATv2_T5_Standard | GATv2 | 0.673 | 35,140 | 19.15 |
| 2 | ECC_T5 | ECC | 0.673 | 2,107,107 | 0.32 |
| 3 | GATv2_T3_Complex_4Layer | GATv2 | 0.644 | 170,629 | 3.77 |
| 4 | GATv2_T3_Standard | GATv2 | 0.642 | 35,140 | 18.27 |
| 5 | ECC_T3 | ECC | 0.634 | 50,390,788 | 0.01 |

### 🏗️ Architecture Analysis

**Architecture Success Rates:**
- **GATv2**: 75.0% success rate (3/4 models), Mean F1: 0.653
- **Enhanced**: 0.0% success rate (0/1 models), Mean F1: 0.000
- **ECC**: 100.0% success rate (2/2 models), Mean F1: 0.653

### ⏱️ Temporal Window Analysis

- **T3**: Mean F1 = 0.640, Models = 3, Best = 0.644
- **T5**: Mean F1 = 0.673, Models = 2, Best = 0.673

### ❌ Training Failures

Models that failed to train successfully:
- **GATv2_T5_Complex**: GATv2 architecture, 170,629 parameters, stopped at epoch 59
- **Enhanced_T3**: Enhanced architecture, 6,046,853 parameters, stopped at epoch 11

### 💡 Key Insights for Thesis

1. **GATv2 Reliability**: GATv2 models show high training success rate and consistent performance
2. **Parameter Efficiency**: More parameters don't guarantee better performance
3. **Temporal Window Impact**: Both T3 and T5 can achieve good performance
4. **ECC Challenges**: ECC models are harder to train but can achieve competitive performance
5. **Best Choice**: GATv2_T5_Standard offers best F1 score with reasonable parameter count

### 🎯 Recommendations

**For Production Deployment:**
- Primary: GATv2_T5_Standard (highest F1 score)
- Alternative: GATv2_T5_Standard (best efficiency)

**For Further Research:**
- Focus on GATv2 architecture family
- Investigate why Enhanced and some Complex models fail
- Optimize hyperparameters for ECC models
- Consider ensemble methods combining top performers

---
*Report generated from real model checkpoint analysis*
