#!/usr/bin/env python3
"""
Fixed Spatial Occupancy Visualization Generator
Creates truly random spatial visualizations for ALL 7 models using exact confusion matrix values.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import os
import glob
from typing import Dict, Tuple, List
import warnings
warnings.filterwarnings('ignore')

class SpatialOccupancyVisualizer:
    """Generate spatial occupancy visualizations for all models with random predictions."""
    
    def __init__(self):
        # Real arena dimensions
        self.arena_bounds = {
            'x_min': -9.1, 'x_max': 10.2,  # 19.3m width
            'y_min': -4.42, 'y_max': 5.5   # 9.92m depth
        }
        
        # EXACT confusion matrix values from user's breakdown
        self.model_confusion_matrices = {
            'ECC_T3': {
                'tp': 1148, 'tn': 658, 'fp': 959, 'fn': 206,
                'temporal_window': 3, 'display_name': 'ECC Model T3'
            },
            'ECC_T5': {
                'tp': 1035, 'tn': 897, 'fp': 701, 'fn': 330,
                'temporal_window': 5, 'display_name': 'ECC Model T5'
            },
            'GATv2_Complex_T3': {
                'tp': 1135, 'tn': 831, 'fp': 786, 'fn': 219,
                'temporal_window': 3, 'display_name': 'Complex GATv2 T3'
            },
            'GATv2_Complex_T5': {
                'tp': 943, 'tn': 1132, 'fp': 466, 'fn': 422,
                'temporal_window': 5, 'display_name': 'Complex GATv2 T5'
            },
            'Enhanced_GATv2_T3': {
                'tp': 1130, 'tn': 868, 'fp': 749, 'fn': 224,
                'temporal_window': 3, 'display_name': 'Enhanced GATv2 T3'
            },
            'GATv2_Standard_T3': {
                'tp': 923, 'tn': 1241, 'fp': 376, 'fn': 431,
                'temporal_window': 3, 'display_name': 'Standard GATv2 T3'
            },
            'GATv2_Standard_T5': {
                'tp': 1141, 'tn': 751, 'fp': 847, 'fn': 224,
                'temporal_window': 5, 'display_name': 'Standard GATv2 T5'
            }
        }
        
        # Color scheme: Red for unoccupied as requested
        self.colors = {
            'occupied': '#1f77b4',      # Blue for occupied
            'unoccupied': '#ff0000',    # Red for unoccupied
            'background': '#ffffff'      # White background
        }
        
        # Create output directory
        self.output_dir = 'spatial_occupancy_visualizations'
        os.makedirs(self.output_dir, exist_ok=True)
    
    def load_ground_truth_data(self):
        """Load ground truth data from temporal_1 folder."""
        gt_dir = 'data/07_gnn_ready/test/temporal_1'
        
        if not os.path.exists(gt_dir):
            raise FileNotFoundError(f"Ground truth directory not found: {gt_dir}")
        
        gt_files = glob.glob(os.path.join(gt_dir, '*.pt'))
        if not gt_files:
            raise FileNotFoundError(f"No .pt files found in {gt_dir}")
        
        # Use exactly 2900 files as specified
        all_files = sorted(gt_files)[:2900]
        
        print(f"📂 Loading ground truth data from {gt_dir}")
        print(f"   Processing {len(all_files)} .pt files for ground truth")
        
        return self._load_files(all_files, "ground truth")
    
    def _load_files(self, file_list: List[str], data_type: str):
        """Helper function to load files and extract spatial data."""
        
        all_positions = []
        all_labels = []
        loaded_count = 0
        
        # Process files in the dataset
        for file_path in file_list:
            try:
                data = torch.load(file_path, map_location='cpu', weights_only=False)
                
                # Extract spatial positions (x, y coordinates)
                if hasattr(data, 'pos') and data.pos is not None:
                    positions = data.pos[:, :2]  # Take only x, y coordinates
                else:
                    print(f"Warning: No position data in {file_path}")
                    continue
                
                # Convert labels to binary occupancy using GROUND TRUTH LABELS
                # Labels: 0=unknown (unoccupied), 1=workstation (occupied), 2=robot (occupied), 3=boundary (occupied)
                binary_labels = (data.y > 0).float()  # >0 means occupied
                
                # Store data
                all_positions.append(positions)
                all_labels.append(binary_labels)
                
                loaded_count += 1
                
                # Progress indicator for large dataset
                if loaded_count % 500 == 0:
                    print(f"   Processed {loaded_count}/{len(file_list)} files...")
                
            except Exception as e:
                print(f"Warning: Could not load {file_path}: {e}")
                continue
        
        if not all_positions:
            raise RuntimeError(f"No valid spatial data loaded for {data_type}")
        
        # Concatenate all data
        positions = torch.cat(all_positions, dim=0)
        labels = torch.cat(all_labels, dim=0)
        
        print(f"✅ {data_type.upper()} dataset loaded:")
        print(f"   - Files processed: {loaded_count}")
        print(f"   - Total nodes: {len(positions):,}")
        print(f"   - Occupied nodes: {torch.sum(labels).item():,} ({torch.mean(labels):.1%})")
        print(f"   - Unoccupied nodes: {len(positions) - torch.sum(labels).item():,} ({1-torch.mean(labels):.1%})")
        print(f"   - Spatial range: X[{positions[:, 0].min():.1f}, {positions[:, 0].max():.1f}], "
              f"Y[{positions[:, 1].min():.1f}, {positions[:, 1].max():.1f}]")
        
        return positions, labels
    
    def generate_random_predictions(self, ground_truth: torch.Tensor, model_name: str) -> torch.Tensor:
        """Generate completely random predictions following exact confusion matrix values."""
        model_cm = self.model_confusion_matrices[model_name]
        
        # Extract exact confusion matrix values
        target_tp = model_cm['tp']
        target_tn = model_cm['tn'] 
        target_fp = model_cm['fp']
        target_fn = model_cm['fn']
        
        num_samples = len(ground_truth)
        total_positive = torch.sum(ground_truth).item()
        total_negative = num_samples - total_positive
        
        print(f"   Target confusion matrix: TP={target_tp}, TN={target_tn}, FP={target_fp}, FN={target_fn}")
        print(f"   Dataset: {num_samples} total, {total_positive} positive, {total_negative} negative")
        
        # Scale the exact confusion matrix values to match current dataset size
        original_total = target_tp + target_tn + target_fp + target_fn
        scale_factor = num_samples / original_total
        
        # Scale each value proportionally to match exact confusion matrix
        scaled_tp = int(target_tp * scale_factor)
        scaled_tn = int(target_tn * scale_factor)
        scaled_fp = int(target_fp * scale_factor)
        scaled_fn = int(target_fn * scale_factor)
        
        # Ensure we don't exceed available samples and maintain exact ratios
        scaled_tp = min(scaled_tp, total_positive)
        scaled_fn = total_positive - scaled_tp  # Ensure TP + FN = total_positive
        scaled_fp = min(scaled_fp, total_negative)
        scaled_tn = total_negative - scaled_fp  # Ensure FP + TN = total_negative
        
        print(f"   Scaled confusion matrix: TP={scaled_tp}, TN={scaled_tn}, FP={scaled_fp}, FN={scaled_fn}")
        
        # Generate completely random predictions following exact confusion matrix
        predictions = torch.zeros(num_samples)
        
        # Get indices for positive and negative ground truth
        positive_indices = torch.where(ground_truth == 1)[0]
        negative_indices = torch.where(ground_truth == 0)[0]
        
        # RANDOM True Positives - completely random selection from positive samples
        if len(positive_indices) > 0 and scaled_tp > 0:
            # Use different random seed for each model to ensure different patterns
            torch.manual_seed(hash(model_name) % 2**32)
            random_pos_indices = positive_indices[torch.randperm(len(positive_indices))]
            tp_indices = random_pos_indices[:scaled_tp]
            predictions[tp_indices] = 1
        
        # RANDOM False Positives - completely random selection from negative samples  
        if len(negative_indices) > 0 and scaled_fp > 0:
            # Use different random seed for each model
            torch.manual_seed((hash(model_name) + 12345) % 2**32)
            random_neg_indices = negative_indices[torch.randperm(len(negative_indices))]
            fp_indices = random_neg_indices[:scaled_fp]
            predictions[fp_indices] = 1
        
        # Add model-specific random noise to make each model unique
        # Each model gets different noise pattern
        noise_seed = hash(model_name + "_noise") % 2**32
        torch.manual_seed(noise_seed)
        
        # Random noise level between 1-3% for each model
        noise_ratio = 0.01 + (noise_seed % 50) / 2500  # 1-3% noise
        noise_count = int(noise_ratio * num_samples)
        
        if noise_count > 0:
            noise_indices = torch.randperm(num_samples)[:noise_count]
            predictions[noise_indices] = 1 - predictions[noise_indices]
        
        # Reset random seed to ensure different patterns for next model
        import time
        torch.manual_seed(int(time.time() * 1000) % 2**32)
        
        # Verify the actual confusion matrix
        actual_tp = torch.sum((predictions == 1) & (ground_truth == 1)).item()
        actual_fp = torch.sum((predictions == 1) & (ground_truth == 0)).item()
        actual_tn = torch.sum((predictions == 0) & (ground_truth == 0)).item()
        actual_fn = torch.sum((predictions == 0) & (ground_truth == 1)).item()
        
        print(f"   Actual confusion matrix: TP={actual_tp}, TN={actual_tn}, FP={actual_fp}, FN={actual_fn}")
        
        # Calculate actual metrics
        accuracy = (actual_tp + actual_tn) / num_samples
        precision = actual_tp / (actual_tp + actual_fp) if (actual_tp + actual_fp) > 0 else 0
        recall = actual_tp / (actual_tp + actual_fn) if (actual_tp + actual_fn) > 0 else 0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        print(f"   Random prediction metrics: Acc={accuracy:.3f}, Prec={precision:.3f}, Rec={recall:.3f}, F1={f1:.3f}")
        
        return predictions

    def create_spatial_comparison_plot(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                                     predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create side-by-side spatial comparison plot."""
        # Create figure with side-by-side subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))

        # Convert to numpy for plotting
        pos_np = positions.numpy()
        gt_np = ground_truth.numpy()
        pred_np = predictions.numpy()

        # Plot 1: Ground Truth
        occupied_mask_gt = gt_np == 1
        unoccupied_mask_gt = gt_np == 0

        ax1.scatter(pos_np[unoccupied_mask_gt, 0], pos_np[unoccupied_mask_gt, 1],
                   c=self.colors['unoccupied'], s=8, alpha=0.7, label='Unoccupied')
        ax1.scatter(pos_np[occupied_mask_gt, 0], pos_np[occupied_mask_gt, 1],
                   c=self.colors['occupied'], s=8, alpha=0.8, label='Occupied')

        ax1.set_title('Ground Truth Occupancy', fontsize=16, fontweight='bold')
        ax1.set_xlabel('X Position (meters)', fontsize=12)
        ax1.set_ylabel('Y Position (meters)', fontsize=12)
        ax1.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
        ax1.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_aspect('equal')

        # Plot 2: Model Predictions
        occupied_mask_pred = pred_np == 1
        unoccupied_mask_pred = pred_np == 0

        ax2.scatter(pos_np[unoccupied_mask_pred, 0], pos_np[unoccupied_mask_pred, 1],
                   c=self.colors['unoccupied'], s=8, alpha=0.7, label='Unoccupied')
        ax2.scatter(pos_np[occupied_mask_pred, 0], pos_np[occupied_mask_pred, 1],
                   c=self.colors['occupied'], s=8, alpha=0.8, label='Occupied')

        # Calculate metrics for title
        accuracy = torch.mean((predictions == ground_truth).float()).item()

        ax2.set_title(f'{model_info["display_name"]} Predictions\nAccuracy: {accuracy:.3f}',
                     fontsize=16, fontweight='bold')
        ax2.set_xlabel('X Position (meters)', fontsize=12)
        ax2.set_ylabel('Y Position (meters)', fontsize=12)
        ax2.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
        ax2.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_aspect('equal')

        # Add arena boundary rectangle
        for ax in [ax1, ax2]:
            arena_rect = patches.Rectangle(
                (self.arena_bounds['x_min'], self.arena_bounds['y_min']),
                self.arena_bounds['x_max'] - self.arena_bounds['x_min'],
                self.arena_bounds['y_max'] - self.arena_bounds['y_min'],
                linewidth=2, edgecolor='black', facecolor='none', linestyle='--'
            )
            ax.add_patch(arena_rect)

        plt.suptitle(f'Spatial Occupancy Comparison - {model_info["display_name"]}',
                    fontsize=18, fontweight='bold')
        plt.tight_layout()

        # Save the plot
        filename = f'spatial_occupancy_{model_name}.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved spatial visualization: {filepath}")
        return filepath

    def generate_all_spatial_visualizations(self):
        """Generate spatial visualizations for ALL 7 models with random predictions."""
        print("🎯 GENERATING SPATIAL OCCUPANCY VISUALIZATIONS FOR ALL 7 MODELS")
        print("=" * 80)
        print("Using exactly 2900 frames")
        print("Ground Truth: temporal_1 folder for all models")
        print("Predictions: RANDOM realistic behavior following exact confusion matrix")
        print("2-class visualization: Occupied (blue) vs Unoccupied (red)")
        print("=" * 80)

        all_results = {}

        # Load ground truth data once (from temporal_1 folder)
        print(f"\n📊 Loading Ground Truth Data...")
        gt_positions, ground_truth = self.load_ground_truth_data()

        # Process ALL 7 models
        for model_name, model_info in self.model_confusion_matrices.items():
            print(f"\n📊 Processing {model_name}...")

            # Generate RANDOM predictions for this model
            print(f"   Generating RANDOM predictions for {len(ground_truth):,} nodes...")
            predictions = self.generate_random_predictions(ground_truth, model_name)

            # Create spatial comparison plot using ground truth positions
            filepath = self.create_spatial_comparison_plot(gt_positions, ground_truth, predictions,
                                                         model_name, model_info)

            # Calculate metrics
            accuracy = torch.mean((predictions == ground_truth).float()).item()
            tp = torch.sum((predictions == 1) & (ground_truth == 1)).item()
            fp = torch.sum((predictions == 1) & (ground_truth == 0)).item()
            tn = torch.sum((predictions == 0) & (ground_truth == 0)).item()
            fn = torch.sum((predictions == 0) & (ground_truth == 1)).item()

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

            all_results[model_name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'total_nodes': len(gt_positions),
                'occupied_nodes': torch.sum(ground_truth).item(),
                'filepath': filepath,
                'model_info': model_info
            }

            print(f"   ✅ Spatial accuracy: {accuracy:.3f}, F1: {f1:.3f}")
            print(f"   📍 Complete dataset: {len(gt_positions):,} total nodes, {torch.sum(ground_truth).item():,} occupied")

        return all_results


def main():
    """Main execution function."""
    print("🎯 SPATIAL OCCUPANCY VISUALIZATION GENERATOR - FIXED VERSION")
    print("=" * 80)
    print("Creating RANDOM spatial visualizations for ALL 7 models")
    print("Using exactly 2900 frames with random predictions")
    print("Following exact confusion matrix values")
    print("2-class visualization: Occupied vs Unoccupied (RED)")
    print("=" * 80)

    # Initialize visualizer
    visualizer = SpatialOccupancyVisualizer()

    # Generate all spatial visualizations
    results = visualizer.generate_all_spatial_visualizations()

    if not results:
        print("❌ No spatial visualizations generated")
        return

    print(f"\n🎉 SPATIAL VISUALIZATION GENERATION COMPLETE!")
    print("=" * 80)
    print(f"📁 Results saved in '{visualizer.output_dir}/' directory:")
    print("   Individual spatial comparisons:")
    for model_name in results.keys():
        print(f"   - spatial_occupancy_{model_name}.png")
    print("=" * 80)

    # Print summary
    print(f"\n💡 SPATIAL PERFORMANCE SUMMARY (ALL 7 MODELS):")
    sorted_results = sorted(results.items(), key=lambda x: x[1]['accuracy'], reverse=True)

    for rank, (model_name, result) in enumerate(sorted_results, 1):
        status = "🥇 BEST" if rank == 1 else "🥉 WORST" if rank == len(sorted_results) else f"#{rank}"
        print(f"   {status} {result['model_info']['display_name']}: "
              f"Spatial Acc={result['accuracy']:.3f}, Nodes={result['total_nodes']:,}")


if __name__ == "__main__":
    main()
