#!/usr/bin/env python3
"""
Quick runner script for the GNN evaluation framework.
This script handles the evaluation with proper error handling and progress tracking.
"""

import sys
import os
import traceback
from gnn_evaluation_framework import main

def check_dependencies():
    """Check if required packages are installed."""
    required_packages = [
        'torch', 'torch_geometric', 'numpy', 'matplotlib', 
        'seaborn', 'pandas', 'yaml'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'torch_geometric':
                import torch_geometric
            elif package == 'yaml':
                import yaml
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements_evaluation.txt")
        return False
    
    return True

def check_data_structure():
    """Check if required data and model files exist."""
    required_paths = [
        'models_final/',
        'data/07_gnn_ready/test/temporal_3/',
        'data/07_gnn_ready/test/temporal_5/'
    ]
    
    missing_paths = []
    for path in required_paths:
        if not os.path.exists(path):
            missing_paths.append(path)
    
    if missing_paths:
        print("❌ Missing required directories:")
        for path in missing_paths:
            print(f"   - {path}")
        print("\n💡 Make sure you're running from the correct directory with model and data files.")
        return False
    
    return True

if __name__ == "__main__":
    print("🔍 GNN Evaluation Framework - Pre-flight Check")
    print("=" * 50)
    
    # Check dependencies
    print("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ All dependencies found")
    
    # Check data structure
    print("Checking data structure...")
    if not check_data_structure():
        sys.exit(1)
    print("✅ Data structure verified")
    
    print("\n🚀 Starting evaluation...")
    print("=" * 50)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Evaluation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Evaluation failed with error:")
        print(f"   {str(e)}")
        print("\n🔧 Full traceback:")
        traceback.print_exc()
        sys.exit(1)
