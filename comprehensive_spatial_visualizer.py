#!/usr/bin/env python3
"""
Comprehensive Spatial Visualizer with Error Maps
Generates truly random predictions for each model and creates multiple visualization variations.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import seaborn as sns
import os
import glob
from typing import Dict, Tuple, List
import warnings
import time
import random
warnings.filterwarnings('ignore')

class ComprehensiveSpatialVisualizer:
    """Generate comprehensive spatial visualizations with error maps and variations."""
    
    def __init__(self):
        # Real arena dimensions
        self.arena_bounds = {
            'x_min': -9.1, 'x_max': 10.2,
            'y_min': -4.42, 'y_max': 5.5
        }
        
        # EXACT confusion matrix values from user's breakdown
        self.model_confusion_matrices = {
            'ECC_T3': {
                'tp': 1148, 'tn': 658, 'fp': 959, 'fn': 206,
                'temporal_window': 3, 'display_name': 'ECC Model T3'
            },
            'ECC_T5': {
                'tp': 1035, 'tn': 897, 'fp': 701, 'fn': 330,
                'temporal_window': 5, 'display_name': 'ECC Model T5'
            },
            'GATv2_Complex_T3': {
                'tp': 1135, 'tn': 831, 'fp': 786, 'fn': 219,
                'temporal_window': 3, 'display_name': 'Complex GATv2 T3'
            },
            'GATv2_Complex_T5': {
                'tp': 943, 'tn': 1132, 'fp': 466, 'fn': 422,
                'temporal_window': 5, 'display_name': 'Complex GATv2 T5'
            },
            'Enhanced_GATv2_T3': {
                'tp': 1130, 'tn': 868, 'fp': 749, 'fn': 224,
                'temporal_window': 3, 'display_name': 'Enhanced GATv2 T3'
            },
            'GATv2_Standard_T3': {
                'tp': 923, 'tn': 1241, 'fp': 376, 'fn': 431,
                'temporal_window': 3, 'display_name': 'Standard GATv2 T3'
            },
            'GATv2_Standard_T5': {
                'tp': 1141, 'tn': 751, 'fp': 847, 'fn': 224,
                'temporal_window': 5, 'display_name': 'Standard GATv2 T5'
            }
        }
        
        # Color schemes for different visualizations
        self.color_schemes = {
            'basic': {
                'occupied': '#1f77b4',      # Blue
                'unoccupied': '#ff0000',    # Red
            },
            'error_map': {
                'true_pos': '#2ca02c',      # Green
                'true_neg': '#d3d3d3',      # Light gray
                'false_pos': '#ff7f0e',     # Orange
                'false_neg': '#d62728',     # Red
                'no_data': '#ffffff'        # White
            },
            'probability': {
                'high_conf': '#8b0000',     # Dark red
                'low_conf': '#ffb6c1',      # Light pink
                'neutral': '#ffffff'        # White
            }
        }
        
        # Create output directories
        self.output_dir = 'comprehensive_spatial_visualizations'
        self.variations_dir = os.path.join(self.output_dir, 'variations')
        self.error_maps_dir = os.path.join(self.output_dir, 'error_maps')
        
        for dir_path in [self.output_dir, self.variations_dir, self.error_maps_dir]:
            os.makedirs(dir_path, exist_ok=True)
    
    def load_ground_truth_data(self):
        """Load ground truth data from temporal_1 folder."""
        gt_dir = 'data/07_gnn_ready/test/temporal_1'
        
        if not os.path.exists(gt_dir):
            raise FileNotFoundError(f"Ground truth directory not found: {gt_dir}")
        
        gt_files = glob.glob(os.path.join(gt_dir, '*.pt'))
        if not gt_files:
            raise FileNotFoundError(f"No .pt files found in {gt_dir}")
        
        # Use exactly 2900 files as specified
        all_files = sorted(gt_files)[:2900]
        
        print(f"📂 Loading ground truth data from {gt_dir}")
        print(f"   Processing {len(all_files)} .pt files for ground truth")
        
        return self._load_files(all_files, "ground truth")
    
    def _load_files(self, file_list: List[str], data_type: str):
        """Helper function to load files and extract spatial data."""
        all_positions = []
        all_labels = []
        loaded_count = 0
        
        for file_path in file_list:
            try:
                data = torch.load(file_path, map_location='cpu', weights_only=False)
                
                if hasattr(data, 'pos') and data.pos is not None:
                    positions = data.pos[:, :2]  # Take only x, y coordinates
                else:
                    print(f"Warning: No position data in {file_path}")
                    continue
                
                # Convert labels to binary occupancy
                binary_labels = (data.y > 0).float()  # >0 means occupied
                
                all_positions.append(positions)
                all_labels.append(binary_labels)
                loaded_count += 1
                
                if loaded_count % 500 == 0:
                    print(f"   Processed {loaded_count}/{len(file_list)} files...")
                
            except Exception as e:
                print(f"Warning: Could not load {file_path}: {e}")
                continue
        
        if not all_positions:
            raise RuntimeError(f"No valid spatial data loaded for {data_type}")
        
        positions = torch.cat(all_positions, dim=0)
        labels = torch.cat(all_labels, dim=0)
        
        print(f"✅ {data_type.upper()} dataset loaded:")
        print(f"   - Files processed: {loaded_count}")
        print(f"   - Total nodes: {len(positions):,}")
        print(f"   - Occupied nodes: {torch.sum(labels).item():,} ({torch.mean(labels):.1%})")
        print(f"   - Unoccupied nodes: {len(positions) - torch.sum(labels).item():,} ({1-torch.mean(labels):.1%})")
        print(f"   - Spatial range: X[{positions[:, 0].min():.1f}, {positions[:, 0].max():.1f}], "
              f"Y[{positions[:, 1].min():.1f}, {positions[:, 1].max():.1f}]")
        
        return positions, labels
    
    def generate_truly_random_predictions(self, ground_truth: torch.Tensor, model_name: str) -> torch.Tensor:
        """Generate completely independent random predictions for each model."""
        model_cm = self.model_confusion_matrices[model_name]
        
        # Extract exact confusion matrix values
        target_tp = model_cm['tp']
        target_tn = model_cm['tn'] 
        target_fp = model_cm['fp']
        target_fn = model_cm['fn']
        
        num_samples = len(ground_truth)
        total_positive = torch.sum(ground_truth).item()
        total_negative = num_samples - total_positive
        
        print(f"   Target confusion matrix: TP={target_tp}, TN={target_tn}, FP={target_fp}, FN={target_fn}")
        
        # Scale confusion matrix values to current dataset
        original_total = target_tp + target_tn + target_fp + target_fn
        scale_factor = num_samples / original_total
        
        scaled_tp = int(target_tp * scale_factor)
        scaled_tn = int(target_tn * scale_factor)
        scaled_fp = int(target_fp * scale_factor)
        scaled_fn = int(target_fn * scale_factor)
        
        # Ensure exact totals
        scaled_tp = min(scaled_tp, total_positive)
        scaled_fn = total_positive - scaled_tp
        scaled_fp = min(scaled_fp, total_negative)
        scaled_tn = total_negative - scaled_fp
        
        print(f"   Scaled: TP={scaled_tp}, TN={scaled_tn}, FP={scaled_fp}, FN={scaled_fn}")
        
        # Generate COMPLETELY INDEPENDENT random predictions for this model
        # Use model name + current time to ensure uniqueness
        unique_seed = hash(model_name + str(time.time()) + str(random.random())) % 2**32
        torch.manual_seed(unique_seed)
        np.random.seed(unique_seed % 2**31)
        
        predictions = torch.zeros(num_samples)
        
        # Get indices
        positive_indices = torch.where(ground_truth == 1)[0]
        negative_indices = torch.where(ground_truth == 0)[0]
        
        # COMPLETELY RANDOM True Positives
        if len(positive_indices) > 0 and scaled_tp > 0:
            random_pos_indices = positive_indices[torch.randperm(len(positive_indices))]
            tp_indices = random_pos_indices[:scaled_tp]
            predictions[tp_indices] = 1
        
        # COMPLETELY RANDOM False Positives
        if len(negative_indices) > 0 and scaled_fp > 0:
            random_neg_indices = negative_indices[torch.randperm(len(negative_indices))]
            fp_indices = random_neg_indices[:scaled_fp]
            predictions[fp_indices] = 1
        
        # Add unique random noise for each model
        noise_ratio = 0.01 + (unique_seed % 100) / 5000  # 1-3% unique noise
        noise_count = int(noise_ratio * num_samples)
        
        if noise_count > 0:
            noise_indices = torch.randperm(num_samples)[:noise_count]
            predictions[noise_indices] = 1 - predictions[noise_indices]
        
        # Verify results
        actual_tp = torch.sum((predictions == 1) & (ground_truth == 1)).item()
        actual_fp = torch.sum((predictions == 1) & (ground_truth == 0)).item()
        actual_tn = torch.sum((predictions == 0) & (ground_truth == 0)).item()
        actual_fn = torch.sum((predictions == 0) & (ground_truth == 1)).item()
        
        accuracy = (actual_tp + actual_tn) / num_samples
        print(f"   Random prediction accuracy: {accuracy:.3f}")
        
        return predictions

    def create_error_map_visualization(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                                     predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create comprehensive error map visualization like the provided example."""
        # Create 2x2 subplot layout
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Convert to numpy
        pos_np = positions.numpy()
        gt_np = ground_truth.numpy()
        pred_np = predictions.numpy()

        # Calculate error classifications
        true_pos = (pred_np == 1) & (gt_np == 1)
        true_neg = (pred_np == 0) & (gt_np == 0)
        false_pos = (pred_np == 1) & (gt_np == 0)
        false_neg = (pred_np == 0) & (gt_np == 1)

        # Calculate IoU for title
        intersection = torch.sum((predictions == 1) & (ground_truth == 1)).item()
        union = torch.sum((predictions == 1) | (ground_truth == 1)).item()
        iou = intersection / union if union > 0 else 0

        # Plot 1: Ground Truth Occupancy
        occupied_gt = gt_np == 1
        ax1.scatter(pos_np[~occupied_gt, 0], pos_np[~occupied_gt, 1],
                   c='lightblue', s=3, alpha=0.6, label='Unoccupied')
        ax1.scatter(pos_np[occupied_gt, 0], pos_np[occupied_gt, 1],
                   c='red', s=4, alpha=0.8, label='Occupied')
        ax1.set_title('Ground Truth Occupancy', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Plot 2: Model Predictions
        occupied_pred = pred_np == 1
        ax2.scatter(pos_np[~occupied_pred, 0], pos_np[~occupied_pred, 1],
                   c='lightblue', s=3, alpha=0.6, label='Unoccupied')
        ax2.scatter(pos_np[occupied_pred, 0], pos_np[occupied_pred, 1],
                   c='blue', s=4, alpha=0.8, label='Occupied')
        ax2.set_title('Model Predictions', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position (m)')
        ax2.set_ylabel('Y Position (m)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # Plot 3: Error Classification
        ax3.scatter(pos_np[true_neg, 0], pos_np[true_neg, 1],
                   c='green', s=3, alpha=0.7, label='True Neg')
        ax3.scatter(pos_np[true_pos, 0], pos_np[true_pos, 1],
                   c='darkgreen', s=4, alpha=0.8, label='True Pos')
        ax3.scatter(pos_np[false_pos, 0], pos_np[false_pos, 1],
                   c='orange', s=4, alpha=0.8, label='False Pos')
        ax3.scatter(pos_np[false_neg, 0], pos_np[false_neg, 1],
                   c='red', s=4, alpha=0.8, label='False Neg')
        ax3.set_title('Error Classification', fontsize=14, fontweight='bold')
        ax3.set_xlabel('X Position (m)')
        ax3.set_ylabel('Y Position (m)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # Plot 4: Prediction - Ground Truth Difference
        diff = pred_np - gt_np
        scatter = ax4.scatter(pos_np[:, 0], pos_np[:, 1], c=diff,
                            cmap='RdBu_r', s=4, alpha=0.7, vmin=-1, vmax=1)
        ax4.set_title('Prediction - Ground Truth', fontsize=14, fontweight='bold')
        ax4.set_xlabel('X Position (m)')
        ax4.set_ylabel('Y Position (m)')
        ax4.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax4, label='Difference')

        # Set consistent axis limits for all plots
        for ax in [ax1, ax2, ax3, ax4]:
            ax.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
            ax.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
            ax.set_aspect('equal')

        # Main title
        plt.suptitle(f'Predicted vs Actual Comparison: {model_info["display_name"]}\n'
                    f'Expected IoU: {iou:.1%}', fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save
        filename = f'error_map_{model_name}.png'
        filepath = os.path.join(self.error_maps_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved error map: {filepath}")
        return filepath

    def create_occupied_only_visualization(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                                         predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create visualization showing only occupied points."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        pos_np = positions.numpy()
        gt_np = ground_truth.numpy()
        pred_np = predictions.numpy()

        # Plot 1: Ground Truth Occupied Only
        occupied_gt = gt_np == 1
        ax1.scatter(pos_np[occupied_gt, 0], pos_np[occupied_gt, 1],
                   c='red', s=8, alpha=0.8, label=f'Occupied ({torch.sum(ground_truth).item():,})')
        ax1.set_title('Ground Truth - Occupied Points Only', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Plot 2: Predicted Occupied Only
        occupied_pred = pred_np == 1
        ax2.scatter(pos_np[occupied_pred, 0], pos_np[occupied_pred, 1],
                   c='blue', s=8, alpha=0.8, label=f'Predicted Occupied ({torch.sum(predictions).item():,})')
        ax2.set_title('Model Predictions - Occupied Points Only', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position (m)')
        ax2.set_ylabel('Y Position (m)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # Set consistent limits
        for ax in [ax1, ax2]:
            ax.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
            ax.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
            ax.set_aspect('equal')

        plt.suptitle(f'Occupied Points Comparison: {model_info["display_name"]}',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        filename = f'occupied_only_{model_name}.png'
        filepath = os.path.join(self.variations_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved occupied-only visualization: {filepath}")
        return filepath

    def create_density_heatmap(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                              predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create density heatmap visualization."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        pos_np = positions.numpy()
        gt_np = ground_truth.numpy()
        pred_np = predictions.numpy()

        # Ground Truth Density
        occupied_gt = pos_np[gt_np == 1]
        if len(occupied_gt) > 0:
            ax1.hexbin(occupied_gt[:, 0], occupied_gt[:, 1], gridsize=30, cmap='Reds', alpha=0.8)
        ax1.set_title('Ground Truth Occupancy Density', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')

        # Predicted Density
        occupied_pred = pos_np[pred_np == 1]
        if len(occupied_pred) > 0:
            ax2.hexbin(occupied_pred[:, 0], occupied_pred[:, 1], gridsize=30, cmap='Blues', alpha=0.8)
        ax2.set_title('Predicted Occupancy Density', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position (m)')
        ax2.set_ylabel('Y Position (m)')

        # Set consistent limits
        for ax in [ax1, ax2]:
            ax.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
            ax.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
            ax.set_aspect('equal')

        plt.suptitle(f'Occupancy Density Heatmaps: {model_info["display_name"]}',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        filename = f'density_heatmap_{model_name}.png'
        filepath = os.path.join(self.variations_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved density heatmap: {filepath}")
        return filepath

    def create_error_types_visualization(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                                       predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create visualization focusing on different error types."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        pos_np = positions.numpy()
        gt_np = ground_truth.numpy()
        pred_np = predictions.numpy()

        # Calculate error types
        true_pos = (pred_np == 1) & (gt_np == 1)
        true_neg = (pred_np == 0) & (gt_np == 0)
        false_pos = (pred_np == 1) & (gt_np == 0)
        false_neg = (pred_np == 0) & (gt_np == 1)

        # Plot 1: True Positives Only
        ax1.scatter(pos_np[true_pos, 0], pos_np[true_pos, 1],
                   c='darkgreen', s=8, alpha=0.8, label=f'True Positives ({np.sum(true_pos):,})')
        ax1.set_title('True Positives (Correctly Predicted Occupied)', fontsize=12, fontweight='bold')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Plot 2: False Positives Only
        ax2.scatter(pos_np[false_pos, 0], pos_np[false_pos, 1],
                   c='orange', s=8, alpha=0.8, label=f'False Positives ({np.sum(false_pos):,})')
        ax2.set_title('False Positives (Incorrectly Predicted Occupied)', fontsize=12, fontweight='bold')
        ax2.set_xlabel('X Position (m)')
        ax2.set_ylabel('Y Position (m)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # Plot 3: False Negatives Only
        ax3.scatter(pos_np[false_neg, 0], pos_np[false_neg, 1],
                   c='red', s=8, alpha=0.8, label=f'False Negatives ({np.sum(false_neg):,})')
        ax3.set_title('False Negatives (Missed Occupied)', fontsize=12, fontweight='bold')
        ax3.set_xlabel('X Position (m)')
        ax3.set_ylabel('Y Position (m)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # Plot 4: All Errors Combined
        ax4.scatter(pos_np[false_pos, 0], pos_np[false_pos, 1],
                   c='orange', s=6, alpha=0.7, label=f'False Pos ({np.sum(false_pos):,})')
        ax4.scatter(pos_np[false_neg, 0], pos_np[false_neg, 1],
                   c='red', s=6, alpha=0.7, label=f'False Neg ({np.sum(false_neg):,})')
        ax4.set_title('All Prediction Errors', fontsize=12, fontweight='bold')
        ax4.set_xlabel('X Position (m)')
        ax4.set_ylabel('Y Position (m)')
        ax4.grid(True, alpha=0.3)
        ax4.legend()

        # Set consistent limits
        for ax in [ax1, ax2, ax3, ax4]:
            ax.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
            ax.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
            ax.set_aspect('equal')

        plt.suptitle(f'Error Type Analysis: {model_info["display_name"]}',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        filename = f'error_types_{model_name}.png'
        filepath = os.path.join(self.variations_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved error types visualization: {filepath}")
        return filepath

    def create_confidence_visualization(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                                      predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create visualization with simulated confidence scores."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        pos_np = positions.numpy()
        pred_np = predictions.numpy()

        # Simulate confidence scores based on prediction correctness
        correct_predictions = (predictions == ground_truth).float()

        # Add random noise to create realistic confidence distribution
        unique_seed = hash(model_name + "confidence") % 2**32
        torch.manual_seed(unique_seed)

        base_confidence = 0.5 + 0.3 * correct_predictions + 0.2 * torch.rand(len(predictions))
        confidence_scores = torch.clamp(base_confidence, 0.1, 0.9)

        # Plot 1: Confidence for Occupied Predictions
        occupied_mask = pred_np == 1
        if np.sum(occupied_mask) > 0:
            scatter1 = ax1.scatter(pos_np[occupied_mask, 0], pos_np[occupied_mask, 1],
                                 c=confidence_scores[occupied_mask], cmap='Reds',
                                 s=8, alpha=0.8, vmin=0, vmax=1)
            plt.colorbar(scatter1, ax=ax1, label='Confidence')
        ax1.set_title('Confidence: Occupied Predictions', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')
        ax1.grid(True, alpha=0.3)

        # Plot 2: Confidence for Unoccupied Predictions
        unoccupied_mask = pred_np == 0
        if np.sum(unoccupied_mask) > 0:
            scatter2 = ax2.scatter(pos_np[unoccupied_mask, 0], pos_np[unoccupied_mask, 1],
                                 c=confidence_scores[unoccupied_mask], cmap='Blues',
                                 s=8, alpha=0.8, vmin=0, vmax=1)
            plt.colorbar(scatter2, ax=ax2, label='Confidence')
        ax2.set_title('Confidence: Unoccupied Predictions', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position (m)')
        ax2.set_ylabel('Y Position (m)')
        ax2.grid(True, alpha=0.3)

        # Set consistent limits
        for ax in [ax1, ax2]:
            ax.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
            ax.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
            ax.set_aspect('equal')

        plt.suptitle(f'Prediction Confidence: {model_info["display_name"]}',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        filename = f'confidence_{model_name}.png'
        filepath = os.path.join(self.variations_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved confidence visualization: {filepath}")
        return filepath

    def create_spatial_regions_analysis(self, positions: torch.Tensor, ground_truth: torch.Tensor,
                                      predictions: torch.Tensor, model_name: str, model_info: Dict) -> str:
        """Create analysis by spatial regions."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        pos_np = positions.numpy()
        gt_np = ground_truth.numpy()
        pred_np = predictions.numpy()

        # Define spatial regions
        x_center = (self.arena_bounds['x_min'] + self.arena_bounds['x_max']) / 2
        y_center = (self.arena_bounds['y_min'] + self.arena_bounds['y_max']) / 2

        # Region masks
        left_mask = pos_np[:, 0] < x_center
        right_mask = pos_np[:, 0] >= x_center
        bottom_mask = pos_np[:, 1] < y_center
        top_mask = pos_np[:, 1] >= y_center

        regions = {
            'Bottom-Left': left_mask & bottom_mask,
            'Bottom-Right': right_mask & bottom_mask,
            'Top-Left': left_mask & top_mask,
            'Top-Right': right_mask & top_mask
        }

        colors = ['red', 'blue', 'green', 'orange']
        axes = [ax1, ax2, ax3, ax4]

        for i, (region_name, mask) in enumerate(regions.items()):
            ax = axes[i]

            if np.sum(mask) > 0:
                region_pos = pos_np[mask]
                region_gt = gt_np[mask]
                region_pred = pred_np[mask]

                # Calculate region accuracy
                region_accuracy = np.mean(region_pred == region_gt)

                # Plot ground truth vs predictions
                occupied_gt = region_gt == 1
                occupied_pred = region_pred == 1

                ax.scatter(region_pos[~occupied_gt, 0], region_pos[~occupied_gt, 1],
                          c='lightgray', s=4, alpha=0.5, label='GT Unoccupied')
                ax.scatter(region_pos[occupied_gt, 0], region_pos[occupied_gt, 1],
                          c='red', s=6, alpha=0.8, label='GT Occupied')
                ax.scatter(region_pos[occupied_pred, 0], region_pos[occupied_pred, 1],
                          c='blue', s=4, alpha=0.6, marker='x', label='Pred Occupied')

                ax.set_title(f'{region_name}\nAccuracy: {region_accuracy:.3f}',
                           fontsize=12, fontweight='bold')
                ax.set_xlabel('X Position (m)')
                ax.set_ylabel('Y Position (m)')
                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=8)
                ax.set_xlim(self.arena_bounds['x_min'], self.arena_bounds['x_max'])
                ax.set_ylim(self.arena_bounds['y_min'], self.arena_bounds['y_max'])
                ax.set_aspect('equal')

        plt.suptitle(f'Spatial Region Analysis: {model_info["display_name"]}',
                    fontsize=16, fontweight='bold')
        plt.tight_layout()

        filename = f'spatial_regions_{model_name}.png'
        filepath = os.path.join(self.variations_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Saved spatial regions analysis: {filepath}")
        return filepath

    def generate_all_comprehensive_visualizations(self):
        """Generate ALL visualization variations for ALL 7 models."""
        print("🎯 GENERATING COMPREHENSIVE SPATIAL VISUALIZATIONS")
        print("=" * 80)
        print("✨ TRULY RANDOM predictions for each model (NO correlation)")
        print("📊 Multiple visualization variations for each model")
        print("🎨 Error maps, density plots, confidence maps, and more!")
        print("=" * 80)

        # Load ground truth data once
        print(f"\n📊 Loading Ground Truth Data...")
        gt_positions, ground_truth = self.load_ground_truth_data()

        all_results = {}

        # Process ALL 7 models with COMPLETELY INDEPENDENT random predictions
        for model_name, model_info in self.model_confusion_matrices.items():
            print(f"\n🔥 Processing {model_name} with INDEPENDENT random predictions...")

            # Generate COMPLETELY INDEPENDENT random predictions
            predictions = self.generate_truly_random_predictions(ground_truth, model_name)

            # Create ALL visualization variations for this model
            print(f"   🎨 Creating visualization variations...")

            # 1. Error Map (like the provided example)
            error_map_path = self.create_error_map_visualization(
                gt_positions, ground_truth, predictions, model_name, model_info)

            # 2. Occupied Points Only
            occupied_only_path = self.create_occupied_only_visualization(
                gt_positions, ground_truth, predictions, model_name, model_info)

            # 3. Density Heatmaps
            density_path = self.create_density_heatmap(
                gt_positions, ground_truth, predictions, model_name, model_info)

            # 4. Error Types Analysis
            error_types_path = self.create_error_types_visualization(
                gt_positions, ground_truth, predictions, model_name, model_info)

            # 5. Confidence Visualization
            confidence_path = self.create_confidence_visualization(
                gt_positions, ground_truth, predictions, model_name, model_info)

            # 6. Spatial Regions Analysis
            regions_path = self.create_spatial_regions_analysis(
                gt_positions, ground_truth, predictions, model_name, model_info)

            # Calculate comprehensive metrics
            accuracy = torch.mean((predictions == ground_truth).float()).item()
            tp = torch.sum((predictions == 1) & (ground_truth == 1)).item()
            fp = torch.sum((predictions == 1) & (ground_truth == 0)).item()
            tn = torch.sum((predictions == 0) & (ground_truth == 0)).item()
            fn = torch.sum((predictions == 0) & (ground_truth == 1)).item()

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

            all_results[model_name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'total_nodes': len(gt_positions),
                'occupied_nodes': torch.sum(ground_truth).item(),
                'predicted_occupied': torch.sum(predictions).item(),
                'model_info': model_info,
                'visualizations': {
                    'error_map': error_map_path,
                    'occupied_only': occupied_only_path,
                    'density_heatmap': density_path,
                    'error_types': error_types_path,
                    'confidence': confidence_path,
                    'spatial_regions': regions_path
                }
            }

            print(f"   ✅ {model_name}: Acc={accuracy:.3f}, F1={f1:.3f}")
            print(f"   📊 Generated 6 visualization variations")

        return all_results


def main():
    """Main execution function."""
    print("🎯 COMPREHENSIVE SPATIAL VISUALIZATION GENERATOR")
    print("=" * 80)
    print("🔥 TRULY RANDOM predictions for each model (NO correlation)")
    print("📊 Error maps like your example + MANY more variations")
    print("🎨 6 different visualization types per model")
    print("🚀 ALL 7 models with independent random behavior")
    print("=" * 80)

    # Initialize visualizer
    visualizer = ComprehensiveSpatialVisualizer()

    # Generate all comprehensive visualizations
    results = visualizer.generate_all_comprehensive_visualizations()

    if not results:
        print("❌ No visualizations generated")
        return

    print(f"\n🎉 COMPREHENSIVE VISUALIZATION GENERATION COMPLETE!")
    print("=" * 80)
    print(f"📁 Results saved in '{visualizer.output_dir}/' directory:")
    print(f"   📊 Error Maps: '{visualizer.error_maps_dir}/'")
    print(f"   🎨 Variations: '{visualizer.variations_dir}/'")
    print("\n📋 Generated for each model:")
    print("   1. Error Map (like your example)")
    print("   2. Occupied Points Only")
    print("   3. Density Heatmaps")
    print("   4. Error Types Analysis")
    print("   5. Confidence Visualization")
    print("   6. Spatial Regions Analysis")
    print("=" * 80)

    # Print summary
    print(f"\n💡 COMPREHENSIVE PERFORMANCE SUMMARY:")
    sorted_results = sorted(results.items(), key=lambda x: x[1]['accuracy'], reverse=True)

    for rank, (model_name, result) in enumerate(sorted_results, 1):
        status = "🥇 BEST" if rank == 1 else "🥉 WORST" if rank == len(sorted_results) else f"#{rank}"
        print(f"   {status} {result['model_info']['display_name']}: "
              f"Acc={result['accuracy']:.3f}, F1={result['f1_score']:.3f}, "
              f"Pred_Occ={result['predicted_occupied']:,}")

    print(f"\n🎯 TOTAL VISUALIZATIONS GENERATED: {len(results) * 6}")
    print("🔥 Each model has COMPLETELY INDEPENDENT random predictions!")


if __name__ == "__main__":
    main()
