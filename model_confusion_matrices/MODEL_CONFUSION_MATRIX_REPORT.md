# Model Confusion Matrix Analysis Report

## 🎯 Overview

Comprehensive evaluation of all 7 GNN models on the complete test dataset (~2900 .pt files) with detailed confusion matrices and performance metrics.

## 📊 Dataset Information

- **Total test samples**: 8,938
- **Binary classification**: Occupied vs Unoccupied
- **Evaluation method**: Complete dataset inference

## 🏆 Model Performance Ranking

| Rank | Model | Accuracy | F1-Score | Precision | Recall | Samples |
|------|-------|----------|----------|-----------|--------|---------|
| 1 | Complex GATv2 T3 | 0.716 | 0.696 | 0.685 | 0.708 | 8,938 |
| 2 | Enhanced GATv2 T3 | 0.707 | 0.700 | 0.661 | 0.743 | 8,938 |
| 3 | Complex GATv2 T5 | 0.704 | 0.680 | 0.678 | 0.682 | 14,856 |
| 4 | Standard GATv2 T3 | 0.699 | 0.694 | 0.652 | 0.741 | 8,938 |
| 5 | Standard GATv2 T5 | 0.663 | 0.650 | 0.624 | 0.679 | 14,856 |
| 6 | ECC Model T5 | 0.636 | 0.621 | 0.598 | 0.645 | 14,856 |
| 7 | ECC Model T3 | 0.603 | 0.586 | 0.562 | 0.611 | 8,938 |

## 📈 Detailed Model Analysis

### Complex GATv2 T3

**File**: `confusion_matrix_GATv2_Complex_T3.png`

**Performance Metrics**:
- Accuracy: 0.716
- F1-Score: 0.696
- Precision: 0.685
- Recall: 0.708

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   3497   1336
        Pos   1199   2906
```

**Model Configuration**:
- Temporal Window: T3
- Parameters: 170,629
- Target Accuracy: 72.8%

### Enhanced GATv2 T3

**File**: `confusion_matrix_Enhanced_GATv2_T3.png`

**Performance Metrics**:
- Accuracy: 0.707
- F1-Score: 0.700
- Precision: 0.661
- Recall: 0.743

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   3269   1564
        Pos   1055   3050
```

**Model Configuration**:
- Temporal Window: T3
- Parameters: 45,000
- Target Accuracy: 67.2%

### Complex GATv2 T5

**File**: `confusion_matrix_GATv2_Complex_T5.png`

**Performance Metrics**:
- Accuracy: 0.704
- F1-Score: 0.680
- Precision: 0.678
- Recall: 0.682

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   5776   2221
        Pos   2182   4677
```

**Model Configuration**:
- Temporal Window: T5
- Parameters: 170,629
- Target Accuracy: 70.0%

### Standard GATv2 T3

**File**: `confusion_matrix_GATv2_Standard_T3.png`

**Performance Metrics**:
- Accuracy: 0.699
- F1-Score: 0.694
- Precision: 0.652
- Recall: 0.741

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   3210   1623
        Pos   1064   3041
```

**Model Configuration**:
- Temporal Window: T3
- Parameters: 35,140
- Target Accuracy: 67.0%

### Standard GATv2 T5

**File**: `confusion_matrix_GATv2_Standard_T5.png`

**Performance Metrics**:
- Accuracy: 0.663
- F1-Score: 0.650
- Precision: 0.624
- Recall: 0.679

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   5191   2806
        Pos   2202   4657
```

**Model Configuration**:
- Temporal Window: T5
- Parameters: 35,140
- Target Accuracy: 63.9%

### ECC Model T5

**File**: `confusion_matrix_ECC_T5.png`

**Performance Metrics**:
- Accuracy: 0.636
- F1-Score: 0.621
- Precision: 0.598
- Recall: 0.645

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   5024   2973
        Pos   2435   4424
```

**Model Configuration**:
- Temporal Window: T5
- Parameters: 2,107,107
- Target Accuracy: 65.2%

### ECC Model T3

**File**: `confusion_matrix_ECC_T3.png`

**Performance Metrics**:
- Accuracy: 0.603
- F1-Score: 0.586
- Precision: 0.562
- Recall: 0.611

**Confusion Matrix**:
```
                Predicted
              Neg    Pos
Actual  Neg   2879   1954
        Pos   1597   2508
```

**Model Configuration**:
- Temporal Window: T3
- Parameters: 50,390,788
- Target Accuracy: 60.8%

## 💡 Key Insights

### 🥇 Best Performing Model
**Complex GATv2 T3** achieved the highest accuracy of 0.716 with an F1-score of 0.696.

### 🥉 Lowest Performing Model
**ECC Model T3** had the lowest accuracy of 0.603 with an F1-score of 0.586.

### 📊 Performance Patterns
- **Temporal Window Impact**: Models show varying performance between T3 and T5 configurations
- **Architecture Differences**: GATv2 and ECC models demonstrate distinct performance characteristics
- **Complexity vs Performance**: Parameter count doesn't always correlate with better performance

## 🎯 Usage for Thesis

**Recommended Visualizations**:
1. **Individual Confusion Matrices**: Show detailed performance breakdown for each model
2. **Performance Summary**: Compare all models side-by-side
3. **Best vs Worst**: Highlight the performance range across architectures

**Key Points to Emphasize**:
- Comprehensive evaluation on complete test dataset
- Clear performance hierarchy among different GNN architectures
- Quantitative metrics supporting model selection decisions
- Real-world applicability demonstrated through confusion matrix analysis

---
*Generated from complete dataset evaluation of all 7 GNN models*
