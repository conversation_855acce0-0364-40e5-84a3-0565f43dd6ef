# Spatial Visualization Report
## Ground Truth vs Model Predictions

### 🎯 Visualization Overview

**Format**: Side-by-side spatial visualizations
- **Left side**: Ground truth graph (occupied vs unoccupied)
- **Right side**: Model prediction graph (probability heatmap)
- **One image per model**: 7 total visualizations
- **All test points**: Complete spatial coverage

**Graph Structure**:
- Nodes: Voxel positions in 3D space
- Edges: k-nearest neighbor connectivity
- Colors: Ground truth (red=occupied, blue=unoccupied), Predictions (heatmap)

### 📊 Model Performance Summary

| Rank | Model | F1-Score | Accuracy | Precision | Recall | Nodes | Edges |
|------|-------|----------|----------|-----------|--------|-------|-------|
| 1 | ECC_T3 | 0.994 | 0.995 | 1.000 | 0.988 | 8,938 | 17,976 |
| 2 | ECC_T5 | 0.994 | 0.994 | 1.000 | 0.987 | 14,856 | 59,670 |
| 3 | Enhanced_T3 | 0.962 | 0.965 | 0.939 | 0.987 | 8,938 | 17,976 |
| 4 | GATv2_T5_Standard | 0.648 | 0.498 | 0.479 | 1.000 | 14,856 | 59,670 |
| 5 | GATv2_T5_Complex | 0.645 | 0.492 | 0.476 | 1.000 | 14,856 | 59,670 |
| 6 | GATv2_T3_Standard | 0.633 | 0.467 | 0.463 | 1.000 | 8,938 | 17,976 |
| 7 | GATv2_T3_Complex_4Layer | 0.633 | 0.467 | 0.463 | 1.000 | 8,938 | 17,976 |

### 🖼️ Generated Visualizations

#### ECC_T3
**File**: `spatial_viz_ECC_T3.png`
**Performance**: F1=0.994, Accuracy=0.995
**Architecture**: ECC, Temporal Window: T3
**Parameters**: 50,390,788
**Dataset**: 8,938 nodes, 17,976 edges
**Occupied Ratio**: 45.9%

#### ECC_T5
**File**: `spatial_viz_ECC_T5.png`
**Performance**: F1=0.994, Accuracy=0.994
**Architecture**: ECC, Temporal Window: T5
**Parameters**: 2,107,107
**Dataset**: 14,856 nodes, 59,670 edges
**Occupied Ratio**: 46.2%

#### Enhanced_T3
**File**: `spatial_viz_Enhanced_T3.png`
**Performance**: F1=0.962, Accuracy=0.965
**Architecture**: Enhanced, Temporal Window: T3
**Parameters**: 45,000
**Dataset**: 8,938 nodes, 17,976 edges
**Occupied Ratio**: 45.9%

#### GATv2_T5_Standard
**File**: `spatial_viz_GATv2_T5_Standard.png`
**Performance**: F1=0.648, Accuracy=0.498
**Architecture**: GATv2, Temporal Window: T5
**Parameters**: 35,140
**Dataset**: 14,856 nodes, 59,670 edges
**Occupied Ratio**: 46.2%

#### GATv2_T5_Complex
**File**: `spatial_viz_GATv2_T5_Complex.png`
**Performance**: F1=0.645, Accuracy=0.492
**Architecture**: GATv2, Temporal Window: T5
**Parameters**: 170,629
**Dataset**: 14,856 nodes, 59,670 edges
**Occupied Ratio**: 46.2%

#### GATv2_T3_Standard
**File**: `spatial_viz_GATv2_T3_Standard.png`
**Performance**: F1=0.633, Accuracy=0.467
**Architecture**: GATv2, Temporal Window: T3
**Parameters**: 35,140
**Dataset**: 8,938 nodes, 17,976 edges
**Occupied Ratio**: 45.9%

#### GATv2_T3_Complex_4Layer
**File**: `spatial_viz_GATv2_T3_Complex_4Layer.png`
**Performance**: F1=0.633, Accuracy=0.467
**Architecture**: GATv2, Temporal Window: T3
**Parameters**: 170,629
**Dataset**: 8,938 nodes, 17,976 edges
**Occupied Ratio**: 45.9%

### 🏆 Best Performing Models

#### 1. ECC_T3
- **F1-Score**: 0.994
- **Accuracy**: 0.995
- **Confusion Matrix**: TP=4056, TN=4833, FP=0, FN=49

#### 2. ECC_T5
- **F1-Score**: 0.994
- **Accuracy**: 0.994
- **Confusion Matrix**: TP=6771, TN=7997, FP=0, FN=88

#### 3. Enhanced_T3
- **F1-Score**: 0.962
- **Accuracy**: 0.965
- **Confusion Matrix**: TP=4052, TN=4570, FP=263, FN=53

### 💡 Visualization Insights

1. **Spatial Patterns**: Models show distinct spatial prediction patterns
2. **Graph Structure**: Edge connectivity reveals neighborhood relationships
3. **Prediction Quality**: Color intensity indicates model confidence
4. **Regional Variation**: Different arena areas show varying prediction accuracy
5. **Model Comparison**: Side-by-side format enables direct performance assessment

### 🎯 Usage for Thesis

**Include these visualizations to demonstrate**:
- Spatial understanding capabilities of GNN models
- Graph structure and connectivity patterns
- Model prediction quality across different regions
- Comparison between ground truth and model outputs
- Visual evidence of model performance differences

**Recommended figures for thesis**:
- **Primary**: `spatial_viz_ECC_T3.png` (best performing model)
- **Comparison**: Select 2-3 additional models showing different architectures
- **Summary**: `spatial_visualization_summary.png` (performance comparison)

---
*Spatial visualizations showing ground truth vs model predictions for GNN occupancy prediction*
