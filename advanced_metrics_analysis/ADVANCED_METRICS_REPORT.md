# Advanced GNN Evaluation Metrics Report
## Comprehensive Performance Analysis Beyond Basic F1/Accuracy

### 🎯 Executive Summary

**Models Analyzed**: 7
**Metrics Categories**: Classification, Spatial, Confidence, Regional, Threshold
**Advanced Techniques**: ROC/PR curves, Calibration analysis, Spatial clustering

### 🏆 Top Performers by Advanced Metrics

- **Best F1 Score**: GATv2_T5_Standard (0.860)
- **Best ROC-AUC**: GATv2_T5_Standard (0.809)
- **Best Matthews Correlation**: GATv2_T5_Standard (0.250)

### 📊 Detailed Model Analysis

#### GATv2_T3_Standard

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.815
- Specificity: 0.323
- Precision: 0.822
- F1 Score: 0.819
- Matthews Correlation Coefficient: 0.136
- <PERSON>'s Kappa: 0.136
- Balanced Accuracy: 0.569
- <PERSON><PERSON>'s Index: 0.138

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.732
- PR-AUC (Average Precision): 0.928

**Confidence and Calibration:**
- Mean Confidence: 0.692
- Confidence Std: 0.220
- Prediction Sharpness: 0.250
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.007

**Spatial Analysis:**
- Spatial Coverage Ratio: 0.992
- Mean Cluster Distance (True): 3.735m
- Mean Cluster Distance (Pred): 3.917m

**Arena Region Performance:**
- Boundary: F1=0.824, Accuracy=0.729, Samples=96
- Workstation: F1=0.865, Accuracy=0.762, Samples=21
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

#### GATv2_T3_Complex_4Layer

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.840
- Specificity: 0.387
- Precision: 0.840
- F1 Score: 0.840
- Matthews Correlation Coefficient: 0.227
- Cohen's Kappa: 0.227
- Balanced Accuracy: 0.614
- Youden's Index: 0.227

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.772
- PR-AUC (Average Precision): 0.940

**Confidence and Calibration:**
- Mean Confidence: 0.700
- Confidence Std: 0.233
- Prediction Sharpness: 0.260
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.007

**Spatial Analysis:**
- Spatial Coverage Ratio: 1.000
- Mean Cluster Distance (True): 3.735m
- Mean Cluster Distance (Pred): 3.889m

**Arena Region Performance:**
- Boundary: F1=0.811, Accuracy=0.719, Samples=96
- Workstation: F1=0.833, Accuracy=0.714, Samples=21
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

#### GATv2_T5_Standard

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.879
- Specificity: 0.353
- Precision: 0.841
- F1 Score: 0.860
- Matthews Correlation Coefficient: 0.250
- Cohen's Kappa: 0.249
- Balanced Accuracy: 0.616
- Youden's Index: 0.232

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.809
- PR-AUC (Average Precision): 0.953

**Confidence and Calibration:**
- Mean Confidence: 0.733
- Confidence Std: 0.216
- Prediction Sharpness: 0.274
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.008

**Spatial Analysis:**
- Spatial Coverage Ratio: 1.045
- Mean Cluster Distance (True): 3.791m
- Mean Cluster Distance (Pred): 4.004m

**Arena Region Performance:**
- Boundary: F1=0.854, Accuracy=0.772, Samples=162
- Workstation: F1=0.889, Accuracy=0.800, Samples=35
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

#### GATv2_T5_Complex

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.819
- Specificity: 0.392
- Precision: 0.840
- F1 Score: 0.830
- Matthews Correlation Coefficient: 0.204
- Cohen's Kappa: 0.204
- Balanced Accuracy: 0.606
- Youden's Index: 0.211

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.782
- PR-AUC (Average Precision): 0.945

**Confidence and Calibration:**
- Mean Confidence: 0.709
- Confidence Std: 0.233
- Prediction Sharpness: 0.267
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.000

**Spatial Analysis:**
- Spatial Coverage Ratio: 0.975
- Mean Cluster Distance (True): 3.791m
- Mean Cluster Distance (Pred): 3.991m

**Arena Region Performance:**
- Boundary: F1=0.856, Accuracy=0.778, Samples=162
- Workstation: F1=0.923, Accuracy=0.857, Samples=35
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

#### Enhanced_T3

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.630
- Specificity: 0.452
- Precision: 0.815
- F1 Score: 0.711
- Matthews Correlation Coefficient: 0.068
- Cohen's Kappa: 0.062
- Balanced Accuracy: 0.541
- Youden's Index: 0.082

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.684
- PR-AUC (Average Precision): 0.914

**Confidence and Calibration:**
- Mean Confidence: 0.601
- Confidence Std: 0.282
- Prediction Sharpness: 0.259
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.073

**Spatial Analysis:**
- Spatial Coverage Ratio: 0.773
- Mean Cluster Distance (True): 3.735m
- Mean Cluster Distance (Pred): 4.063m

**Arena Region Performance:**
- Boundary: F1=0.742, Accuracy=0.646, Samples=96
- Workstation: F1=0.800, Accuracy=0.667, Samples=21
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

#### ECC_T3

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.664
- Specificity: 0.581
- Precision: 0.859
- F1 Score: 0.749
- Matthews Correlation Coefficient: 0.203
- Cohen's Kappa: 0.185
- Balanced Accuracy: 0.622
- Youden's Index: 0.245

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.716
- PR-AUC (Average Precision): 0.924

**Confidence and Calibration:**
- Mean Confidence: 0.600
- Confidence Std: 0.271
- Prediction Sharpness: 0.247
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.047

**Spatial Analysis:**
- Spatial Coverage Ratio: 0.773
- Mean Cluster Distance (True): 3.735m
- Mean Cluster Distance (Pred): 3.966m

**Arena Region Performance:**
- Boundary: F1=0.766, Accuracy=0.688, Samples=96
- Workstation: F1=0.800, Accuracy=0.667, Samples=21
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

#### ECC_T5

**Advanced Classification Metrics:**
- Sensitivity/Recall: 0.729
- Specificity: 0.510
- Precision: 0.853
- F1 Score: 0.786
- Matthews Correlation Coefficient: 0.206
- Cohen's Kappa: 0.197
- Balanced Accuracy: 0.619
- Youden's Index: 0.238

**ROC and PR Curve Metrics:**
- ROC-AUC: 0.739
- PR-AUC (Average Precision): 0.931

**Confidence and Calibration:**
- Mean Confidence: 0.621
- Confidence Std: 0.238
- Prediction Sharpness: 0.226
- Overconfidence Rate: 0.000
- Underconfidence Rate: 0.036

**Spatial Analysis:**
- Spatial Coverage Ratio: 0.854
- Mean Cluster Distance (True): 3.791m
- Mean Cluster Distance (Pred): 4.073m

**Arena Region Performance:**
- Boundary: F1=0.814, Accuracy=0.735, Samples=162
- Workstation: F1=0.833, Accuracy=0.714, Samples=35
- Navigation: F1=0.000, Accuracy=0.333, Samples=3

---

### 💡 Advanced Insights for Thesis

1. **Model Calibration**: Analysis of prediction confidence vs actual performance
2. **Spatial Clustering**: How well models capture spatial occupancy patterns
3. **Regional Performance**: Variation across different arena zones
4. **Threshold Sensitivity**: Performance stability across decision thresholds
5. **Advanced Metrics**: Beyond accuracy - MCC, Kappa, Youden's Index

### 🎯 Recommendations for Thesis

**Include these advanced metrics in your evaluation chapter:**
- Matthews Correlation Coefficient (better than F1 for imbalanced data)
- ROC and Precision-Recall curves
- Spatial clustering analysis
- Regional performance breakdown
- Model calibration analysis

---
*Advanced metrics analysis for GNN occupancy prediction models*
