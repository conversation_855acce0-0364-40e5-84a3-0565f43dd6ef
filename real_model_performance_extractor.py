#!/usr/bin/env python3
"""
Real Model Performance Extractor
Extracts actual performance metrics from your trained model checkpoints
and generates individual model analysis reports.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import json
from pathlib import Path
import yaml

class RealModelAnalyzer:
    """Extract real performance data from trained model checkpoints."""
    
    def __init__(self):
        self.model_paths = {
            'GATv2_T3_Standard': 'models_final/checkpoints_gatv2_temp3/model_temporal_3_best.pt',
            'GATv2_T3_Complex_4Layer': 'models_final/checkpoints_gatv2_complex_4layers_temp3/model_temporal_3_best.pt',
            'GATv2_T5_Standard': 'models_final/checkpoints_temp5/model_temporal_5_best.pt',
            'GATv2_T5_Complex': 'models_final/checkpoints_gatv2_complex_temp5/model_temporal_5_best.pt',
            'Enhanced_T3': 'models_final/checkpoints_enhanced_temp3/model_temporal_3_best.pt',
            'ECC_T3': 'models_final/checkpoints_ecc_temp3/model_temporal_3_best.pt',
            'ECC_T5': 'models_final/checkpoints_ecc_temp5/model_temporal_5_best.pt'
        }
        
        self.results = {}
    
    def extract_checkpoint_info(self, model_name: str, checkpoint_path: str) -> dict:
        """Extract all available information from a model checkpoint."""
        try:
            print(f"📊 Analyzing {model_name}...")
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            
            info = {
                'model_name': model_name,
                'checkpoint_path': checkpoint_path,
                'file_exists': True
            }
            
            # Extract basic checkpoint information
            if isinstance(checkpoint, dict):
                info.update({
                    'epoch': checkpoint.get('epoch', 'N/A'),
                    'val_f1': checkpoint.get('val_f1', 'N/A'),
                    'val_loss': checkpoint.get('val_loss', 'N/A'),
                    'val_accuracy': checkpoint.get('val_accuracy', 'N/A'),
                    'val_precision': checkpoint.get('val_precision', 'N/A'),
                    'val_recall': checkpoint.get('val_recall', 'N/A'),
                    'val_roc_auc': checkpoint.get('val_roc_auc', 'N/A'),
                })
                
                # Extract model configuration
                if 'config' in checkpoint:
                    config = checkpoint['config']
                    info['config'] = config
                    
                    # Extract model-specific config
                    if 'model' in config:
                        model_config = config['model']
                        info.update({
                            'input_dim': model_config.get('input_dim', 'N/A'),
                            'hidden_dim': model_config.get('hidden_dim', 'N/A'),
                            'output_dim': model_config.get('output_dim', 'N/A'),
                            'num_layers': model_config.get('num_layers', 'N/A'),
                            'dropout': model_config.get('dropout', 'N/A'),
                            'gnn_type': model_config.get('gnn_type', 'N/A'),
                            'pooling': model_config.get('pooling', 'N/A')
                        })
                
                # Extract model state dict info
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                    total_params = sum(p.numel() for p in state_dict.values())
                    trainable_params = sum(p.numel() for p in state_dict.values() if p.requires_grad)
                    
                    info.update({
                        'total_parameters': total_params,
                        'trainable_parameters': trainable_params,
                        'model_layers': list(state_dict.keys())[:10]  # First 10 layer names
                    })
                
                # Extract training history if available
                if 'train_losses' in checkpoint:
                    info['train_losses'] = checkpoint['train_losses']
                if 'val_losses' in checkpoint:
                    info['val_losses'] = checkpoint['val_losses']
                if 'train_metrics' in checkpoint:
                    info['train_metrics'] = checkpoint['train_metrics']
                if 'val_metrics' in checkpoint:
                    info['val_metrics'] = checkpoint['val_metrics']
            
            print(f"   ✅ Successfully analyzed {model_name}")
            return info
            
        except Exception as e:
            print(f"   ❌ Failed to analyze {model_name}: {str(e)}")
            return {
                'model_name': model_name,
                'checkpoint_path': checkpoint_path,
                'file_exists': os.path.exists(checkpoint_path),
                'error': str(e)
            }
    
    def analyze_all_models(self):
        """Analyze all available models."""
        print("🔍 Extracting Real Model Performance Data")
        print("=" * 60)
        
        for model_name, checkpoint_path in self.model_paths.items():
            if os.path.exists(checkpoint_path):
                self.results[model_name] = self.extract_checkpoint_info(model_name, checkpoint_path)
            else:
                print(f"❌ Model file not found: {checkpoint_path}")
                self.results[model_name] = {
                    'model_name': model_name,
                    'checkpoint_path': checkpoint_path,
                    'file_exists': False,
                    'error': 'File not found'
                }
        
        return self.results
    
    def create_performance_comparison_table(self, save_dir: str = 'real_model_analysis'):
        """Create a comprehensive performance comparison table."""
        os.makedirs(save_dir, exist_ok=True)
        
        # Prepare data for table
        table_data = []
        
        for model_name, info in self.results.items():
            if info.get('file_exists', False) and 'error' not in info:
                row = {
                    'Model': model_name,
                    'Architecture': info.get('gnn_type', 'N/A').upper(),
                    'Parameters': f"{info.get('total_parameters', 0):,}",
                    'Hidden Dim': info.get('hidden_dim', 'N/A'),
                    'Layers': info.get('num_layers', 'N/A'),
                    'Dropout': info.get('dropout', 'N/A'),
                    'Pooling': info.get('pooling', 'N/A'),
                    'Final Epoch': info.get('epoch', 'N/A'),
                    'Val F1': f"{info.get('val_f1', 0):.3f}" if info.get('val_f1') != 'N/A' else 'N/A',
                    'Val Accuracy': f"{info.get('val_accuracy', 0):.3f}" if info.get('val_accuracy') != 'N/A' else 'N/A',
                    'Val Precision': f"{info.get('val_precision', 0):.3f}" if info.get('val_precision') != 'N/A' else 'N/A',
                    'Val Recall': f"{info.get('val_recall', 0):.3f}" if info.get('val_recall') != 'N/A' else 'N/A',
                    'Val ROC-AUC': f"{info.get('val_roc_auc', 0):.3f}" if info.get('val_roc_auc') != 'N/A' else 'N/A'
                }
                table_data.append(row)
        
        # Create DataFrame
        df = pd.DataFrame(table_data)
        
        # Save as CSV
        csv_path = os.path.join(save_dir, 'model_performance_comparison.csv')
        df.to_csv(csv_path, index=False)
        
        # Create formatted table for display
        print("\n📊 REAL MODEL PERFORMANCE COMPARISON")
        print("=" * 120)
        print(df.to_string(index=False))
        
        # Save as markdown table
        md_path = os.path.join(save_dir, 'model_performance_table.md')
        with open(md_path, 'w') as f:
            f.write("# Real Model Performance Comparison\n\n")
            f.write("## Comprehensive Model Analysis\n\n")
            f.write("| Model | Architecture | Parameters | Val F1 | Val Accuracy | Val Precision | Val Recall | Val ROC-AUC |\n")
            f.write("|-------|--------------|------------|--------|--------------|---------------|------------|-------------|\n")
            
            for _, row in df.iterrows():
                f.write(f"| {row['Model']} | {row['Architecture']} | {row['Parameters']} | "
                       f"{row['Val F1']} | {row['Val Accuracy']} | {row['Val Precision']} | "
                       f"{row['Val Recall']} | {row['Val ROC-AUC']} |\n")
            
            f.write("\n## Key Findings\n\n")
            
            # Find best models
            valid_models = df[df['Val F1'] != 'N/A'].copy()
            if not valid_models.empty:
                valid_models['Val F1 Numeric'] = valid_models['Val F1'].astype(float)
                best_f1_model = valid_models.loc[valid_models['Val F1 Numeric'].idxmax()]
                
                f.write(f"- **Best F1 Score**: {best_f1_model['Model']} ({best_f1_model['Val F1']})\n")
                f.write(f"- **Architecture Distribution**: {df['Architecture'].value_counts().to_dict()}\n")
                f.write(f"- **Parameter Range**: {df['Parameters'].min()} to {df['Parameters'].max()}\n")
        
        print(f"\n✅ Saved performance comparison to {save_dir}/")
        return df
    
    def create_individual_model_reports(self, save_dir: str = 'real_model_analysis'):
        """Create detailed reports for each individual model."""
        os.makedirs(save_dir, exist_ok=True)
        
        for model_name, info in self.results.items():
            if info.get('file_exists', False) and 'error' not in info:
                # Create individual model report
                report_path = os.path.join(save_dir, f'{model_name}_detailed_report.md')
                
                with open(report_path, 'w') as f:
                    f.write(f"# {model_name} - Detailed Analysis Report\n\n")
                    
                    f.write("## Model Configuration\n\n")
                    f.write(f"- **Architecture**: {info.get('gnn_type', 'N/A').upper()}\n")
                    f.write(f"- **Total Parameters**: {info.get('total_parameters', 0):,}\n")
                    f.write(f"- **Input Dimension**: {info.get('input_dim', 'N/A')}\n")
                    f.write(f"- **Hidden Dimension**: {info.get('hidden_dim', 'N/A')}\n")
                    f.write(f"- **Output Dimension**: {info.get('output_dim', 'N/A')}\n")
                    f.write(f"- **Number of Layers**: {info.get('num_layers', 'N/A')}\n")
                    f.write(f"- **Dropout Rate**: {info.get('dropout', 'N/A')}\n")
                    f.write(f"- **Pooling Method**: {info.get('pooling', 'N/A')}\n\n")
                    
                    f.write("## Training Results\n\n")
                    f.write(f"- **Final Epoch**: {info.get('epoch', 'N/A')}\n")
                    f.write(f"- **Validation F1**: {info.get('val_f1', 'N/A')}\n")
                    f.write(f"- **Validation Accuracy**: {info.get('val_accuracy', 'N/A')}\n")
                    f.write(f"- **Validation Precision**: {info.get('val_precision', 'N/A')}\n")
                    f.write(f"- **Validation Recall**: {info.get('val_recall', 'N/A')}\n")
                    f.write(f"- **Validation ROC-AUC**: {info.get('val_roc_auc', 'N/A')}\n")
                    f.write(f"- **Validation Loss**: {info.get('val_loss', 'N/A')}\n\n")
                    
                    if 'config' in info:
                        f.write("## Full Configuration\n\n")
                        f.write("```yaml\n")
                        f.write(yaml.dump(info['config'], default_flow_style=False))
                        f.write("```\n\n")
                    
                    f.write("## Model Architecture Layers\n\n")
                    if 'model_layers' in info:
                        for i, layer in enumerate(info['model_layers'], 1):
                            f.write(f"{i}. {layer}\n")
                    
                    f.write("\n---\n")
                    f.write(f"*Report generated from checkpoint: {info['checkpoint_path']}*\n")
        
        print(f"✅ Generated individual model reports in {save_dir}/")
    
    def create_performance_visualizations(self, save_dir: str = 'real_model_analysis'):
        """Create visualizations of real model performance."""
        os.makedirs(save_dir, exist_ok=True)
        
        # Extract data for plotting
        valid_models = []
        for model_name, info in self.results.items():
            if info.get('file_exists', False) and 'error' not in info and info.get('val_f1') != 'N/A':
                valid_models.append({
                    'name': model_name,
                    'architecture': info.get('gnn_type', 'unknown').upper(),
                    'parameters': info.get('total_parameters', 0),
                    'f1': float(info.get('val_f1', 0)),
                    'accuracy': float(info.get('val_accuracy', 0)) if info.get('val_accuracy') != 'N/A' else 0,
                    'precision': float(info.get('val_precision', 0)) if info.get('val_precision') != 'N/A' else 0,
                    'recall': float(info.get('val_recall', 0)) if info.get('val_recall') != 'N/A' else 0,
                    'roc_auc': float(info.get('val_roc_auc', 0)) if info.get('val_roc_auc') != 'N/A' else 0
                })
        
        if not valid_models:
            print("❌ No valid models found for visualization")
            return
        
        # Create comprehensive visualization
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Plot 1: F1 Score comparison
        names = [m['name'].replace('_', '\n') for m in valid_models]
        f1_scores = [m['f1'] for m in valid_models]
        colors = ['red' if 'GATV2' in m['architecture'] else 'blue' if 'ECC' in m['architecture'] else 'green' 
                 for m in valid_models]
        
        bars = axes[0, 0].bar(range(len(names)), f1_scores, color=colors, alpha=0.7)
        axes[0, 0].set_xticks(range(len(names)))
        axes[0, 0].set_xticklabels(names, rotation=45, ha='right')
        axes[0, 0].set_ylabel('F1 Score')
        axes[0, 0].set_title('Model F1 Score Comparison')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Add value labels
        for bar, score in zip(bars, f1_scores):
            axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                           f'{score:.3f}', ha='center', va='bottom')
        
        # Plot 2: Parameter efficiency
        parameters = [m['parameters'] for m in valid_models]
        
        scatter = axes[0, 1].scatter(parameters, f1_scores, c=colors, s=100, alpha=0.7)
        for i, model in enumerate(valid_models):
            axes[0, 1].annotate(model['name'].split('_')[0], 
                               (parameters[i], f1_scores[i]), 
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        axes[0, 1].set_xlabel('Total Parameters')
        axes[0, 1].set_ylabel('F1 Score')
        axes[0, 1].set_title('Parameter Efficiency')
        axes[0, 1].set_xscale('log')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Plot 3: Architecture comparison
        arch_data = {}
        for model in valid_models:
            arch = model['architecture']
            if arch not in arch_data:
                arch_data[arch] = []
            arch_data[arch].append(model['f1'])
        
        arch_names = list(arch_data.keys())
        arch_means = [np.mean(arch_data[arch]) for arch in arch_names]
        arch_stds = [np.std(arch_data[arch]) if len(arch_data[arch]) > 1 else 0 for arch in arch_names]
        
        bars = axes[0, 2].bar(arch_names, arch_means, yerr=arch_stds, capsize=5, alpha=0.7,
                             color=['red', 'blue', 'green'][:len(arch_names)])
        axes[0, 2].set_ylabel('Mean F1 Score')
        axes[0, 2].set_title('Performance by Architecture')
        axes[0, 2].grid(True, alpha=0.3)
        
        # Plot 4: All metrics comparison
        metrics = ['f1', 'accuracy', 'precision', 'recall', 'roc_auc']
        metric_data = {metric: [m[metric] for m in valid_models] for metric in metrics}
        
        x_pos = np.arange(len(valid_models))
        width = 0.15
        
        for i, metric in enumerate(metrics):
            axes[1, 0].bar(x_pos + i*width, metric_data[metric], width, 
                          label=metric.replace('_', ' ').title(), alpha=0.8)
        
        axes[1, 0].set_xlabel('Models')
        axes[1, 0].set_ylabel('Score')
        axes[1, 0].set_title('All Metrics Comparison')
        axes[1, 0].set_xticks(x_pos + width*2)
        axes[1, 0].set_xticklabels([m['name'].split('_')[0] for m in valid_models], rotation=45)
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Plot 5: Model ranking
        ranking = sorted(valid_models, key=lambda x: x['f1'], reverse=True)
        ranking_names = [m['name'] for m in ranking]
        ranking_scores = [m['f1'] for m in ranking]
        ranking_colors = ['red' if 'GATV2' in m['architecture'] else 'blue' if 'ECC' in m['architecture'] else 'green' 
                         for m in ranking]
        
        axes[1, 1].barh(range(len(ranking_names)), ranking_scores, color=ranking_colors, alpha=0.7)
        axes[1, 1].set_yticks(range(len(ranking_names)))
        axes[1, 1].set_yticklabels([name.replace('_', ' ') for name in ranking_names])
        axes[1, 1].set_xlabel('F1 Score')
        axes[1, 1].set_title('Model Ranking (Best to Worst)')
        axes[1, 1].grid(True, alpha=0.3)
        
        # Plot 6: Summary statistics
        summary_stats = {
            'Best F1': max(f1_scores),
            'Mean F1': np.mean(f1_scores),
            'Std F1': np.std(f1_scores),
            'Models Count': len(valid_models)
        }
        
        axes[1, 2].bar(range(len(summary_stats)-1), list(summary_stats.values())[:-1], 
                      color=['gold', 'silver', 'bronze'], alpha=0.7)
        axes[1, 2].set_xticks(range(len(summary_stats)-1))
        axes[1, 2].set_xticklabels(list(summary_stats.keys())[:-1])
        axes[1, 2].set_ylabel('F1 Score')
        axes[1, 2].set_title(f'Summary Statistics ({summary_stats["Models Count"]} models)')
        axes[1, 2].grid(True, alpha=0.3)
        
        # Add value labels
        for i, (key, value) in enumerate(list(summary_stats.items())[:-1]):
            axes[1, 2].text(i, value + 0.01, f'{value:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'real_model_performance_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Saved performance visualizations to {save_dir}/")


def main():
    """Main execution function."""
    print("🔍 REAL MODEL PERFORMANCE EXTRACTION")
    print("=" * 60)
    print("Extracting actual performance data from your trained models...")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = RealModelAnalyzer()
    
    # Analyze all models
    results = analyzer.analyze_all_models()
    
    # Create comprehensive analysis
    print("\n📊 Creating comprehensive analysis...")
    df = analyzer.create_performance_comparison_table()
    analyzer.create_individual_model_reports()
    analyzer.create_performance_visualizations()
    
    print("\n🎉 Real Model Analysis Complete!")
    print("=" * 60)
    print("📁 Results saved in 'real_model_analysis/' directory:")
    print("   - model_performance_comparison.csv")
    print("   - model_performance_table.md")
    print("   - [ModelName]_detailed_report.md (for each model)")
    print("   - real_model_performance_analysis.png")
    
    # Print summary
    valid_models = [name for name, info in results.items() 
                   if info.get('file_exists', False) and 'error' not in info]
    
    print(f"\n💡 Summary:")
    print(f"   - Successfully analyzed: {len(valid_models)} models")
    print(f"   - Failed to load: {len(results) - len(valid_models)} models")
    
    if valid_models:
        best_model = max(valid_models, 
                        key=lambda x: results[x].get('val_f1', 0) if results[x].get('val_f1') != 'N/A' else 0)
        best_f1 = results[best_model].get('val_f1', 'N/A')
        print(f"   - Best performing model: {best_model} (F1: {best_f1})")


if __name__ == "__main__":
    main()
