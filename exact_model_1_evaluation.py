#!/usr/bin/env python3
"""
EXACT Model 1: GATv2_T3_Standard - Real Model Loading and Evaluation
Based on actual checkpoint structure analysis.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATv2Conv
import numpy as np
import matplotlib.pyplot as plt
import os
import glob
import warnings
warnings.filterwarnings('ignore')

class ExactGATv2_T3_Standard(nn.Module):
    """EXACT architecture matching the checkpoint structure."""
    
    def __init__(self):
        super().__init__()
        
        # Embedding: 10 -> 64
        self.embedding = nn.Linear(10, 64)
        
        # 3 GATv2 convolution layers: 64 -> 64 (single head)
        self.convs = nn.ModuleList([
            GATv2Conv(64, 64, heads=1, concat=False),
            GATv2Conv(64, 64, heads=1, concat=False),
            GATv2Conv(64, 64, heads=1, concat=False)
        ])
        
        # Batch normalization layers (exact structure)
        self.batch_norms = nn.ModuleList([
            nn.ModuleDict({'module': nn.BatchNorm1d(64)}),
            nn.ModuleDict({'module': nn.BatchNorm1d(64)}),
            nn.ModuleDict({'module': nn.BatchNorm1d(64)})
        ])
        
        # MLP: 128 -> 64 -> 1 (with ReLU and Dropout)
        self.mlp = nn.Sequential(
            nn.Linear(128, 64),    # mlp.0
            nn.ReLU(),             # mlp.1
            nn.Dropout(0.2),       # mlp.2
            nn.Linear(64, 1)       # mlp.3
        )
    
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        # Handle feature dimension (use only first 10 features)
        if x.shape[1] > 10:
            x = x[:, :10]
        
        # Embedding
        x = self.embedding(x)
        x_initial = x.clone()
        
        # GATv2 layers with batch norm
        for conv, bn in zip(self.convs, self.batch_norms):
            x = conv(x, edge_index)
            x = bn['module'](x)
            x = F.relu(x)
        
        # Concatenate initial and final features
        x_concat = torch.cat([x_initial, x], dim=1)
        
        # MLP classifier
        x = self.mlp(x_concat)
        
        return torch.sigmoid(x.squeeze())


def load_all_test_data_t3():
    """Load ALL test data for temporal window 3."""
    test_dir = 'data/07_gnn_ready/test/temporal_3'
    test_files = glob.glob(os.path.join(test_dir, '*.pt'))
    
    print(f"Loading ALL test data from {test_dir}")
    print(f"Found {len(test_files)} .pt files")
    
    test_data = []
    for i, file_path in enumerate(sorted(test_files)):
        try:
            data = torch.load(file_path, map_location='cpu', weights_only=False)
            test_data.append(data)
            
            if (i + 1) % 500 == 0:
                print(f"  Loaded {i + 1}/{len(test_files)} files...")
                
        except Exception as e:
            print(f"Warning: Could not load {file_path}: {e}")
            continue
    
    print(f"✅ Loaded {len(test_data)} test files")
    return test_data


def run_exact_model_1_evaluation():
    """Run EXACT evaluation for Model 1."""
    print("🎯 EXACT MODEL 1: GATv2_T3_Standard - REAL EVALUATION")
    print("=" * 60)
    
    # Load checkpoint
    checkpoint_path = 'models_final/checkpoints_gatv2_temp3/model_temporal_3_best.pt'
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint not found: {checkpoint_path}")
        return None
    
    # Create EXACT model
    model = ExactGATv2_T3_Standard()
    
    # Load weights
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        print(f"✅ EXACT model loaded successfully!")
        print(f"   Validation F1: {checkpoint.get('val_f1', 0):.3f}")
        print(f"   Epoch: {checkpoint.get('epoch', 0)}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        print("State dict keys mismatch:")
        model_keys = set(model.state_dict().keys())
        checkpoint_keys = set(checkpoint['model_state_dict'].keys())
        print(f"Missing in model: {checkpoint_keys - model_keys}")
        print(f"Extra in model: {model_keys - checkpoint_keys}")
        return None
    
    # Load ALL test data
    test_data = load_all_test_data_t3()
    if not test_data:
        print("❌ No test data loaded")
        return None
    
    print(f"📊 Running REAL inference on {len(test_data)} test samples...")
    
    # Run inference
    model.eval()
    all_predictions = []
    all_labels = []
    all_positions = []
    
    with torch.no_grad():
        for i, data in enumerate(test_data):
            try:
                # Run inference
                predictions = model(data)
                
                # Convert labels to binary
                binary_labels = (data.y > 0).float()
                
                # Store results
                all_predictions.append(predictions.cpu())
                all_labels.append(binary_labels.cpu())
                all_positions.append(data.pos.cpu())
                
                if (i + 1) % 500 == 0:
                    print(f"  Processed {i + 1}/{len(test_data)} samples...")
                    
            except Exception as e:
                print(f"Error processing sample {i}: {e}")
                continue
    
    if not all_predictions:
        print("❌ No successful predictions")
        return None
    
    # Concatenate results
    predictions = torch.cat(all_predictions, dim=0)
    labels = torch.cat(all_labels, dim=0)
    positions = torch.cat(all_positions, dim=0)
    
    print(f"✅ REAL inference complete: {len(predictions)} total predictions")
    
    # Calculate metrics
    pred_binary = (predictions > 0.5).float()
    accuracy = torch.mean((pred_binary == labels).float()).item()
    
    # Confusion matrix
    tp = torch.sum((pred_binary == 1) & (labels == 1)).item()
    tn = torch.sum((pred_binary == 0) & (labels == 0)).item()
    fp = torch.sum((pred_binary == 1) & (labels == 0)).item()
    fn = torch.sum((pred_binary == 0) & (labels == 1)).item()
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    metrics = {
        'model_name': 'GATv2_T3_Standard',
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': {'TP': int(tp), 'TN': int(tn), 'FP': int(fp), 'FN': int(fn)},
        'total_samples': len(labels),
        'occupied_samples': int(torch.sum(labels)),
        'test_files_processed': len(test_data),
        'prediction_stats': {
            'mean': float(torch.mean(predictions)),
            'std': float(torch.std(predictions)),
            'min': float(torch.min(predictions)),
            'max': float(torch.max(predictions))
        }
    }
    
    # Print results
    print(f"\n📊 EXACT MODEL 1 RESULTS:")
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Accuracy: {accuracy:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   Total samples: {len(labels):,}")
    print(f"   Test files: {len(test_data):,}")
    print(f"   Prediction range: [{metrics['prediction_stats']['min']:.3f}, {metrics['prediction_stats']['max']:.3f}]")
    
    # Create spatial visualization
    create_spatial_visualization(predictions, labels, positions, metrics)
    
    return {
        'metrics': metrics,
        'predictions': predictions,
        'labels': labels,
        'positions': positions
    }


def create_spatial_visualization(predictions, labels, positions, metrics):
    """Create spatial visualization exactly as requested."""
    print("🎨 Creating spatial visualization...")
    
    os.makedirs('exact_model_results', exist_ok=True)
    
    # Create side-by-side visualization: Ground Truth | Model Predictions
    fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Convert to numpy
    positions_np = positions.numpy()
    labels_np = labels.numpy()
    predictions_np = predictions.numpy()
    
    # Left plot: Ground Truth Graph
    occupied_mask = labels_np == 1
    unoccupied_mask = labels_np == 0
    
    if np.any(unoccupied_mask):
        ax_left.scatter(positions_np[unoccupied_mask, 0], positions_np[unoccupied_mask, 1], 
                       c='lightblue', s=6, alpha=0.7, label='Unoccupied', edgecolors='blue', linewidth=0.1)
    
    if np.any(occupied_mask):
        ax_left.scatter(positions_np[occupied_mask, 0], positions_np[occupied_mask, 1], 
                       c='lightcoral', s=6, alpha=0.7, label='Occupied', edgecolors='red', linewidth=0.1)
    
    ax_left.set_title(f'Ground Truth Graph\n{len(labels_np):,} nodes, {torch.sum(labels).item():,} occupied ({torch.mean(labels):.1%})', 
                     fontsize=14, fontweight='bold')
    ax_left.set_xlabel('X Position (m)', fontsize=12)
    ax_left.set_ylabel('Y Position (m)', fontsize=12)
    ax_left.legend(fontsize=10)
    ax_left.grid(True, alpha=0.3)
    ax_left.set_aspect('equal')
    
    # Right plot: Prediction Graph
    scatter = ax_right.scatter(positions_np[:, 0], positions_np[:, 1], 
                             c=predictions_np, cmap='RdYlBu_r', 
                             s=6, alpha=0.7, vmin=0, vmax=1, 
                             edgecolors='black', linewidth=0.1)
    
    cbar = plt.colorbar(scatter, ax=ax_right, shrink=0.8)
    cbar.set_label('Model Prediction Probability', fontsize=10)
    
    ax_right.set_title(f'GATv2_T3_Standard Prediction Graph\nF1: {metrics["f1_score"]:.3f}, Acc: {metrics["accuracy"]:.3f}, Prec: {metrics["precision"]:.3f}, Rec: {metrics["recall"]:.3f}', 
                      fontsize=14, fontweight='bold')
    ax_right.set_xlabel('X Position (m)', fontsize=12)
    ax_right.set_ylabel('Y Position (m)', fontsize=12)
    ax_right.grid(True, alpha=0.3)
    ax_right.set_aspect('equal')
    
    # Set consistent limits
    x_min, x_max = positions_np[:, 0].min() - 1, positions_np[:, 0].max() + 1
    y_min, y_max = positions_np[:, 1].min() - 1, positions_np[:, 1].max() + 1
    
    for ax in [ax_left, ax_right]:
        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)
    
    # Add model information
    fig.suptitle(f'REAL Model 1: GATv2_T3_Standard - ALL {metrics["test_files_processed"]:,} Test Files\n'
                f'Left: Ground Truth Graph | Right: Prediction Graph', 
                fontsize=16, fontweight='bold')
    
    # Add statistics
    stats_text = f'REAL MODEL INFERENCE:\n'
    stats_text += f'Total Nodes: {metrics["total_samples"]:,}\n'
    stats_text += f'Occupied: {metrics["occupied_samples"]:,} ({metrics["occupied_samples"]/metrics["total_samples"]:.1%})\n'
    stats_text += f'Prediction Range: [{metrics["prediction_stats"]["min"]:.3f}, {metrics["prediction_stats"]["max"]:.3f}]\n'
    stats_text += f'Mean ± Std: {metrics["prediction_stats"]["mean"]:.3f} ± {metrics["prediction_stats"]["std"]:.3f}'
    
    fig.text(0.02, 0.02, stats_text, fontsize=10, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8))
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.85, bottom=0.15)
    
    # Save visualization
    filename = 'exact_model_1_GATv2_T3_Standard_spatial_visualization.png'
    filepath = os.path.join('exact_model_results', filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Saved REAL spatial visualization: {filepath}")


if __name__ == "__main__":
    result = run_exact_model_1_evaluation()
    if result:
        print("\n🎉 EXACT Model 1 evaluation complete!")
        print("This is REAL model inference - no fake predictions!")
    else:
        print("\n❌ EXACT Model 1 evaluation failed!")
