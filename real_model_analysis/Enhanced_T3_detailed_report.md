# Enhanced_T3 - Detailed Analysis Report

## Model Configuration

- **Architecture**: N/A
- **Total Parameters**: 6,046,853
- **Input Dimension**: 10
- **Hidden Dimension**: 192
- **Output Dimension**: 1
- **Number of Layers**: 4
- **Dropout Rate**: 0.15
- **Pooling Method**: hierarchical

## Training Results

- **Final Epoch**: 11
- **Validation F1**: N/A
- **Validation Accuracy**: N/A
- **Validation Precision**: N/A
- **Validation Recall**: N/A
- **Validation ROC-AUC**: N/A
- **Validation Loss**: N/A

## Full Configuration

```yaml
ablation:
  feature_importance: true
  gnn_types:
  - enhanced
  temporal_windows:
  - 3
data:
  augmentation:
    rotation_angle: 15
    scaling_range:
    - 0.9
    - 1.1
  batch_size: 16
  binary_mapping:
    occupied:
    - 1
    - 2
    - 3
    - 4
    unoccupied:
    - 0
  data_dir: /home/<USER>/ma_yugi/data/07_gnn_ready/
  num_workers: 4
  temporal_windows:
  - 3
evaluation:
  metrics:
  - accuracy
  - precision
  - recall
  - f1
  - roc_auc
  visualization:
    num_samples: 5
    save_dir: /home/<USER>/ma_yugi/gnn_occupancy/visualizations_enhanced_temp3/
model:
  attention_heads: 8
  batch_norm: true
  dropout: 0.15
  hidden_dim: 192
  input_dim: 10
  layer_norm: true
  name: EnhancedOccupancyGNN
  num_layers: 4
  output_dim: 1
  pool_ratios:
  - 0.8
  - 0.6
  pooling: hierarchical
  skip_connections: true
  use_transformer: true
training:
  checkpoint_dir: /home/<USER>/ma_yugi/gnn_occupancy/checkpoints_enhanced_temp3/
  device: cpu
  early_stopping:
    min_delta: 0.0005
    patience: 25
  epochs: 150
  gradient_clipping:
    enabled: true
    max_norm: 1.0
  learning_rate: 0.0005
  lr_scheduler:
    T_max: 150
    eta_min: 1.0e-05
    type: cosine_annealing
  optimizer:
    betas:
    - 0.9
    - 0.999
    eps: 1.0e-08
    type: adamw
  warmup:
    enabled: true
    warmup_epochs: 10
    warmup_factor: 0.1
  weight_decay: 0.0001
```

## Model Architecture Layers

1. input_embedding.0.weight
2. input_embedding.0.bias
3. input_embedding.1.weight
4. input_embedding.1.bias
5. pos_encoding.0.weight
6. pos_encoding.0.bias
7. pos_encoding.2.weight
8. pos_encoding.2.bias
9. gnn_layers.0.gnn.lin_key.weight
10. gnn_layers.0.gnn.lin_key.bias

---
*Report generated from checkpoint: models_final/checkpoints_enhanced_temp3/model_temporal_3_best.pt*
