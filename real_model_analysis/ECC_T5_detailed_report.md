# ECC_T5 - Detailed Analysis Report

## Model Configuration

- **Architecture**: ECC
- **Total Parameters**: 2,107,107
- **Input Dimension**: 10
- **Hidden Dimension**: 32
- **Output Dimension**: 1
- **Number of Layers**: 2
- **Dropout Rate**: 0.3
- **Pooling Method**: mean

## Training Results

- **Final Epoch**: 63
- **Validation F1**: 0.6732436472346787
- **Validation Accuracy**: N/A
- **Validation Precision**: N/A
- **Validation Recall**: N/A
- **Validation ROC-AUC**: N/A
- **Validation Loss**: N/A

## Full Configuration

```yaml
ablation:
  feature_importance: true
  gnn_types:
  - graphsage
  - gatv2
  - ecc
  temporal_windows:
  - 1
  - 3
  - 5
data:
  augmentation:
    rotation_angle: 15
    scaling_range:
    - 0.9
    - 1.1
  batch_size: 8
  binary_mapping:
    occupied:
    - 1
    - 2
    - 3
    - 4
    unoccupied:
    - 0
  data_dir: /home/<USER>/ma_yugi/data/07_gnn_ready
  num_workers: 4
  temporal_windows:
  - 5
evaluation:
  metrics:
  - accuracy
  - precision
  - recall
  - f1
  - roc_auc
  visualization:
    num_samples: 5
    save_dir: /home/<USER>/ma_yugi/gnn_occupancy/visualizations_ecc_temp5/
model:
  attention_heads: 2
  batch_norm: true
  dropout: 0.3
  gnn_type: ecc
  hidden_dim: 32
  input_dim: 10
  layer_norm: false
  name: OccupancyGNN
  num_layers: 2
  output_dim: 1
  pooling: mean
  skip_connections: false
training:
  checkpoint_dir: /home/<USER>/ma_yugi/gnn_occupancy/checkpoints_ecc_temp5/
  device: cuda
  early_stopping:
    min_delta: 0.001
    patience: 20
  epochs: 100
  learning_rate: 0.001
  lr_scheduler:
    factor: 0.5
    min_lr: 1.0e-05
    patience: 10
  seed: 42
  weight_decay: 0.0001
```

## Model Architecture Layers

1. embedding.weight
2. embedding.bias
3. convs.0.edge_nn.0.weight
4. convs.0.edge_nn.0.bias
5. convs.0.edge_nn.2.weight
6. convs.0.edge_nn.2.bias
7. convs.0.root.weight
8. convs.0.root.bias
9. convs.1.edge_nn.0.weight
10. convs.1.edge_nn.0.bias

---
*Report generated from checkpoint: models_final/checkpoints_ecc_temp5/model_temporal_5_best.pt*
