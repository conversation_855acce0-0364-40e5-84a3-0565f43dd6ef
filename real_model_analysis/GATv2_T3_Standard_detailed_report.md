# GATv2_T3_Standard - Detailed Analysis Report

## Model Configuration

- **Architecture**: GATV2
- **Total Parameters**: 35,140
- **Input Dimension**: 10
- **Hidden Dimension**: 64
- **Output Dimension**: 1
- **Number of Layers**: 3
- **Dropout Rate**: 0.2
- **Pooling Method**: mean_max

## Training Results

- **Final Epoch**: 1
- **Validation F1**: 0.6423995340710542
- **Validation Accuracy**: N/A
- **Validation Precision**: N/A
- **Validation Recall**: N/A
- **Validation ROC-AUC**: N/A
- **Validation Loss**: N/A

## Full Configuration

```yaml
ablation:
  feature_importance: true
  gnn_types:
  - gatv2
  temporal_windows:
  - 3
data:
  augmentation:
    rotation_angle: 15
    scaling_range:
    - 0.9
    - 1.1
  batch_size: 32
  binary_mapping:
    occupied:
    - 1
    - 2
    - 3
    - 4
    unoccupied:
    - 0
  data_dir: /home/<USER>/ma_yugi/data/07_gnn_ready/
  num_workers: 4
  temporal_windows:
  - 3
evaluation:
  metrics:
  - accuracy
  - precision
  - recall
  - f1
  - roc_auc
  visualization:
    num_samples: 5
    save_dir: /home/<USER>/ma_yugi/gnn_occupancy/visualizations_gatv2_temp3/
model:
  batch_norm: true
  dropout: 0.2
  gnn_type: gatv2
  hidden_dim: 64
  input_dim: 10
  name: OccupancyGNN
  num_layers: 3
  output_dim: 1
  pooling: mean_max
  skip_connections: true
training:
  checkpoint_dir: /home/<USER>/ma_yugi/gnn_occupancy/checkpoints_gatv2_temp3/
  device: cpu
  early_stopping:
    min_delta: 0.001
    patience: 20
  epochs: 100
  learning_rate: 0.001
  lr_scheduler:
    factor: 0.5
    min_lr: 1.0e-05
    patience: 10
  seed: 42
  weight_decay: 0.0001
```

## Model Architecture Layers

1. embedding.weight
2. embedding.bias
3. convs.0.att
4. convs.0.bias
5. convs.0.lin_l.weight
6. convs.0.lin_l.bias
7. convs.0.lin_r.weight
8. convs.0.lin_r.bias
9. convs.1.att
10. convs.1.bias

---
*Report generated from checkpoint: models_final/checkpoints_gatv2_temp3/model_temporal_3_best.pt*
