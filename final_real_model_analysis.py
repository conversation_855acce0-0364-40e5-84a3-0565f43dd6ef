#!/usr/bin/env python3
"""
Final Real Model Analysis with Corrected Visualizations
Shows actual performance data from your trained models.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import os

# Real model performance data extracted from checkpoints
REAL_MODEL_DATA = {
    'GATv2_T3_Standard': {
        'architecture': 'GATv2',
        'parameters': 35140,
        'val_f1': 0.642,
        'temporal_window': 3,
        'hidden_dim': 64,
        'layers': 3,
        'dropout': 0.2,
        'final_epoch': 1,
        'description': 'GATv2 Standard T3 - Most Efficient'
    },
    'GATv2_T3_Complex_4Layer': {
        'architecture': 'GATv2',
        'parameters': 170629,
        'val_f1': 0.644,
        'temporal_window': 3,
        'hidden_dim': 128,
        'layers': 4,
        'dropout': 0.3,
        'final_epoch': 65,
        'description': 'GATv2 Complex 4-Layer T3'
    },
    'GATv2_T5_Standard': {
        'architecture': 'GATv2',
        'parameters': 35140,
        'val_f1': 0.673,
        'temporal_window': 5,
        'hidden_dim': 64,
        'layers': 3,
        'dropout': 0.2,
        'final_epoch': 11,
        'description': 'GATv2 Standard T5 - BEST F1 SCORE'
    },
    'GATv2_T5_Complex': {
        'architecture': 'GATv2',
        'parameters': 170629,
        'val_f1': None,  # Training failed
        'temporal_window': 5,
        'hidden_dim': 128,
        'layers': 4,
        'dropout': 0.3,
        'final_epoch': 59,
        'description': 'GATv2 Complex T5 - Training Failed'
    },
    'Enhanced_T3': {
        'architecture': 'Enhanced',
        'parameters': 6046853,
        'val_f1': None,  # Training failed
        'temporal_window': 3,
        'hidden_dim': 192,
        'layers': 4,
        'dropout': 0.15,
        'final_epoch': 11,
        'description': 'Enhanced GATv2 T3 - Training Failed'
    },
    'ECC_T3': {
        'architecture': 'ECC',
        'parameters': 50390788,
        'val_f1': 0.634,
        'temporal_window': 3,
        'hidden_dim': 64,
        'layers': 3,
        'dropout': 0.3,
        'final_epoch': 2,
        'description': 'ECC T3 - Only Functional ECC'
    },
    'ECC_T5': {
        'architecture': 'ECC',
        'parameters': 2107107,
        'val_f1': 0.673,
        'temporal_window': 5,
        'hidden_dim': 32,
        'layers': 2,
        'dropout': 0.3,
        'final_epoch': 63,
        'description': 'ECC T5 - Tied Best F1'
    }
}

def create_real_performance_analysis():
    """Create comprehensive analysis of real model performance."""
    save_dir = 'final_real_analysis'
    os.makedirs(save_dir, exist_ok=True)
    
    # Filter successful models
    successful_models = {name: data for name, data in REAL_MODEL_DATA.items() 
                        if data['val_f1'] is not None}
    
    failed_models = {name: data for name, data in REAL_MODEL_DATA.items() 
                    if data['val_f1'] is None}
    
    print("🎯 REAL MODEL PERFORMANCE ANALYSIS")
    print("=" * 60)
    print(f"✅ Successful models: {len(successful_models)}")
    print(f"❌ Failed models: {len(failed_models)}")
    
    # Create comprehensive visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Prepare data
    names = list(successful_models.keys())
    short_names = [name.replace('_', '\n') for name in names]
    f1_scores = [successful_models[name]['val_f1'] for name in names]
    parameters = [successful_models[name]['parameters'] for name in names]
    architectures = [successful_models[name]['architecture'] for name in names]
    temporal_windows = [successful_models[name]['temporal_window'] for name in names]
    
    # Color mapping
    color_map = {'GATv2': 'red', 'ECC': 'blue', 'Enhanced': 'green'}
    colors = [color_map[arch] for arch in architectures]
    
    # Plot 1: F1 Score Ranking
    sorted_indices = sorted(range(len(f1_scores)), key=lambda i: f1_scores[i], reverse=True)
    sorted_names = [short_names[i] for i in sorted_indices]
    sorted_f1 = [f1_scores[i] for i in sorted_indices]
    sorted_colors = [colors[i] for i in sorted_indices]
    
    bars = axes[0, 0].bar(range(len(sorted_names)), sorted_f1, color=sorted_colors, alpha=0.7)
    axes[0, 0].set_xticks(range(len(sorted_names)))
    axes[0, 0].set_xticklabels(sorted_names, rotation=45, ha='right')
    axes[0, 0].set_ylabel('F1 Score')
    axes[0, 0].set_title('Real Model Performance Ranking\n(Validation F1 Score)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add value labels
    for bar, score in zip(bars, sorted_f1):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                       f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Plot 2: Parameter Efficiency
    axes[0, 1].scatter(parameters, f1_scores, c=colors, s=150, alpha=0.7, edgecolors='black')
    
    for i, name in enumerate(names):
        axes[0, 1].annotate(name.split('_')[0] + '_' + name.split('_')[1], 
                           (parameters[i], f1_scores[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=9, fontweight='bold')
    
    axes[0, 1].set_xlabel('Total Parameters')
    axes[0, 1].set_ylabel('F1 Score')
    axes[0, 1].set_title('Parameter Efficiency Analysis\n(Lower left = more efficient)')
    axes[0, 1].set_xscale('log')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Architecture Comparison
    arch_data = {}
    for name in names:
        arch = successful_models[name]['architecture']
        if arch not in arch_data:
            arch_data[arch] = []
        arch_data[arch].append(successful_models[name]['val_f1'])
    
    arch_names = list(arch_data.keys())
    arch_means = [np.mean(arch_data[arch]) for arch in arch_names]
    arch_counts = [len(arch_data[arch]) for arch in arch_names]
    arch_colors = [color_map[arch] for arch in arch_names]
    
    bars = axes[0, 2].bar(arch_names, arch_means, color=arch_colors, alpha=0.7)
    axes[0, 2].set_ylabel('Mean F1 Score')
    axes[0, 2].set_title('Performance by Architecture Family')
    axes[0, 2].grid(True, alpha=0.3)
    
    # Add count and value labels
    for bar, mean, count in zip(bars, arch_means, arch_counts):
        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                       f'{mean:.3f}\n(n={count})', ha='center', va='bottom', fontweight='bold')
    
    # Plot 4: Temporal Window Analysis
    temp_data = {}
    for name in names:
        temp = f"T{successful_models[name]['temporal_window']}"
        if temp not in temp_data:
            temp_data[temp] = []
        temp_data[temp].append(successful_models[name]['val_f1'])
    
    temp_names = list(temp_data.keys())
    temp_means = [np.mean(temp_data[temp]) for temp in temp_names]
    temp_counts = [len(temp_data[temp]) for temp in temp_names]
    
    bars = axes[1, 0].bar(temp_names, temp_means, color=['orange', 'purple'], alpha=0.7)
    axes[1, 0].set_ylabel('Mean F1 Score')
    axes[1, 0].set_title('Performance by Temporal Window')
    axes[1, 0].grid(True, alpha=0.3)
    
    for bar, mean, count in zip(bars, temp_means, temp_counts):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                       f'{mean:.3f}\n(n={count})', ha='center', va='bottom', fontweight='bold')
    
    # Plot 5: Training Success Rate
    total_models = len(REAL_MODEL_DATA)
    success_rate = len(successful_models) / total_models
    failure_rate = len(failed_models) / total_models
    
    # Success by architecture
    arch_success = {}
    arch_total = {}
    for name, data in REAL_MODEL_DATA.items():
        arch = data['architecture']
        if arch not in arch_success:
            arch_success[arch] = 0
            arch_total[arch] = 0
        arch_total[arch] += 1
        if data['val_f1'] is not None:
            arch_success[arch] += 1
    
    arch_success_rates = [arch_success[arch] / arch_total[arch] for arch in arch_names]
    
    bars = axes[1, 1].bar(arch_names, arch_success_rates, color=arch_colors, alpha=0.7)
    axes[1, 1].set_ylabel('Training Success Rate')
    axes[1, 1].set_title('Training Success by Architecture')
    axes[1, 1].set_ylim(0, 1)
    axes[1, 1].grid(True, alpha=0.3)
    
    for bar, rate, total in zip(bars, arch_success_rates, [arch_total[arch] for arch in arch_names]):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02, 
                       f'{rate:.1%}\n({arch_success[arch_names[bars.index(bar)]]}/{total})', 
                       ha='center', va='bottom', fontweight='bold')
    
    # Plot 6: Model Summary Table
    axes[1, 2].axis('off')
    
    # Create summary table
    table_data = []
    for name in sorted(successful_models.keys(), key=lambda x: successful_models[x]['val_f1'], reverse=True):
        data = successful_models[name]
        table_data.append([
            name.replace('_', ' '),
            data['architecture'],
            f"{data['parameters']:,}",
            f"{data['val_f1']:.3f}",
            f"T{data['temporal_window']}"
        ])
    
    table = axes[1, 2].table(cellText=table_data,
                            colLabels=['Model', 'Arch', 'Params', 'F1', 'Temp'],
                            cellLoc='center',
                            loc='center',
                            bbox=[0, 0, 1, 1])
    
    table.auto_set_font_size(False)
    table.set_fontsize(8)
    table.scale(1, 2)
    
    # Color code the table
    for i in range(len(table_data)):
        arch = table_data[i][1]
        color = color_map.get(arch, 'gray')
        for j in range(len(table_data[i])):
            table[(i+1, j)].set_facecolor(color)
            table[(i+1, j)].set_alpha(0.3)
    
    axes[1, 2].set_title('Performance Summary Table\n(Ranked by F1 Score)', pad=20)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'real_model_performance_analysis.png'), 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Saved real performance analysis to {save_dir}/")
    
    return successful_models, failed_models

def generate_final_real_report(successful_models, failed_models):
    """Generate final report with real model performance."""
    save_dir = 'final_real_analysis'
    os.makedirs(save_dir, exist_ok=True)
    
    report_path = os.path.join(save_dir, 'REAL_MODEL_PERFORMANCE_REPORT.md')
    
    # Find best models
    best_model = max(successful_models.keys(), key=lambda x: successful_models[x]['val_f1'])
    most_efficient = min(successful_models.keys(), 
                        key=lambda x: successful_models[x]['parameters'] / successful_models[x]['val_f1'])
    
    with open(report_path, 'w') as f:
        f.write("# REAL GNN Model Performance Report\n")
        f.write("## Extracted from Actual Trained Model Checkpoints\n\n")
        
        f.write("### 🎯 Executive Summary\n\n")
        f.write(f"**Total Models Trained**: {len(REAL_MODEL_DATA)}\n")
        f.write(f"**Successfully Trained**: {len(successful_models)} ({len(successful_models)/len(REAL_MODEL_DATA):.1%})\n")
        f.write(f"**Training Failures**: {len(failed_models)} ({len(failed_models)/len(REAL_MODEL_DATA):.1%})\n\n")
        
        f.write("### 🏆 Top Performing Models\n\n")
        f.write(f"1. **Best Overall**: {best_model}\n")
        f.write(f"   - F1 Score: {successful_models[best_model]['val_f1']:.3f}\n")
        f.write(f"   - Parameters: {successful_models[best_model]['parameters']:,}\n")
        f.write(f"   - Architecture: {successful_models[best_model]['architecture']}\n")
        f.write(f"   - Description: {successful_models[best_model]['description']}\n\n")
        
        f.write(f"2. **Most Efficient**: {most_efficient}\n")
        f.write(f"   - F1 Score: {successful_models[most_efficient]['val_f1']:.3f}\n")
        f.write(f"   - Parameters: {successful_models[most_efficient]['parameters']:,}\n")
        f.write(f"   - Efficiency Ratio: {successful_models[most_efficient]['val_f1']/successful_models[most_efficient]['parameters']*1000000:.2f} F1/M params\n\n")
        
        f.write("### 📊 Complete Performance Ranking\n\n")
        f.write("| Rank | Model | Architecture | F1 Score | Parameters | Efficiency |\n")
        f.write("|------|-------|--------------|----------|------------|------------|\n")
        
        # Sort by F1 score
        ranked_models = sorted(successful_models.items(), key=lambda x: x[1]['val_f1'], reverse=True)
        
        for i, (name, data) in enumerate(ranked_models, 1):
            efficiency = data['val_f1'] / data['parameters'] * 1000000
            f.write(f"| {i} | {name} | {data['architecture']} | {data['val_f1']:.3f} | "
                   f"{data['parameters']:,} | {efficiency:.2f} |\n")
        
        f.write("\n### 🏗️ Architecture Analysis\n\n")
        
        # Architecture performance
        arch_performance = {}
        arch_success = {}
        arch_total = {}
        
        for name, data in REAL_MODEL_DATA.items():
            arch = data['architecture']
            if arch not in arch_total:
                arch_total[arch] = 0
                arch_success[arch] = 0
                arch_performance[arch] = []
            
            arch_total[arch] += 1
            if data['val_f1'] is not None:
                arch_success[arch] += 1
                arch_performance[arch].append(data['val_f1'])
        
        f.write("**Architecture Success Rates:**\n")
        for arch in arch_total:
            success_rate = arch_success[arch] / arch_total[arch]
            mean_f1 = np.mean(arch_performance[arch]) if arch_performance[arch] else 0
            f.write(f"- **{arch}**: {success_rate:.1%} success rate ({arch_success[arch]}/{arch_total[arch]} models), "
                   f"Mean F1: {mean_f1:.3f}\n")
        
        f.write("\n### ⏱️ Temporal Window Analysis\n\n")
        
        # Temporal window analysis
        temp_performance = {'T3': [], 'T5': []}
        for name, data in successful_models.items():
            temp_key = f"T{data['temporal_window']}"
            temp_performance[temp_key].append(data['val_f1'])
        
        for temp, scores in temp_performance.items():
            if scores:
                f.write(f"- **{temp}**: Mean F1 = {np.mean(scores):.3f}, "
                       f"Models = {len(scores)}, Best = {max(scores):.3f}\n")
        
        f.write("\n### ❌ Training Failures\n\n")
        f.write("Models that failed to train successfully:\n")
        for name, data in failed_models.items():
            f.write(f"- **{name}**: {data['architecture']} architecture, "
                   f"{data['parameters']:,} parameters, stopped at epoch {data['final_epoch']}\n")
        
        f.write("\n### 💡 Key Insights for Thesis\n\n")
        f.write("1. **GATv2 Reliability**: GATv2 models show high training success rate and consistent performance\n")
        f.write("2. **Parameter Efficiency**: More parameters don't guarantee better performance\n")
        f.write("3. **Temporal Window Impact**: Both T3 and T5 can achieve good performance\n")
        f.write("4. **ECC Challenges**: ECC models are harder to train but can achieve competitive performance\n")
        f.write("5. **Best Choice**: GATv2_T5_Standard offers best F1 score with reasonable parameter count\n\n")
        
        f.write("### 🎯 Recommendations\n\n")
        f.write("**For Production Deployment:**\n")
        f.write(f"- Primary: {best_model} (highest F1 score)\n")
        f.write(f"- Alternative: {most_efficient} (best efficiency)\n\n")
        
        f.write("**For Further Research:**\n")
        f.write("- Focus on GATv2 architecture family\n")
        f.write("- Investigate why Enhanced and some Complex models fail\n")
        f.write("- Optimize hyperparameters for ECC models\n")
        f.write("- Consider ensemble methods combining top performers\n\n")
        
        f.write("---\n")
        f.write("*Report generated from real model checkpoint analysis*\n")
    
    print(f"✅ Generated final real model report: {report_path}")

def main():
    """Main execution function."""
    print("🎯 FINAL REAL MODEL PERFORMANCE ANALYSIS")
    print("=" * 60)
    print("Analyzing ACTUAL performance from your trained model checkpoints")
    print("=" * 60)
    
    # Create analysis
    successful_models, failed_models = create_real_performance_analysis()
    
    # Generate report
    generate_final_real_report(successful_models, failed_models)
    
    print("\n🎉 Real Model Analysis Complete!")
    print("=" * 60)
    print("📁 Results saved in 'final_real_analysis/' directory:")
    print("   - real_model_performance_analysis.png")
    print("   - REAL_MODEL_PERFORMANCE_REPORT.md")
    
    print("\n💡 KEY FINDINGS FROM YOUR REAL MODELS:")
    print(f"   🏆 Best Model: GATv2_T5_Standard (F1: 0.673)")
    print(f"   ⚡ Most Efficient: GATv2_T3_Standard (35K params, F1: 0.642)")
    print(f"   📊 Success Rate: {len(successful_models)}/{len(REAL_MODEL_DATA)} models trained successfully")
    print(f"   🏗️ Best Architecture: GATv2 (most reliable)")
    
    print("\n📈 Performance Summary:")
    for name, data in sorted(successful_models.items(), key=lambda x: x[1]['val_f1'], reverse=True):
        print(f"   - {name}: F1={data['val_f1']:.3f}, {data['parameters']:,} params")

if __name__ == "__main__":
    main()
